/**
 * Template de e-mail para pagamentos que vencem hoje
 * Tom de urgência moderada mas ainda amigável
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface PaymentDueTodayTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  tenantSlug?: string;

  // Dados do estudante e pagamento
  studentName: string;
  amount: number;
  dueDate: string;
  planName: string;
  paymentMethod?: string;
  invoiceUrl?: string;
  paymentId?: string;

  // Configurações opcionais
  currency?: string;
  gracePeriod?: number;
}

export function PaymentDueTodayTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  tenantSlug,
  studentName,
  amount,
  dueDate,
  planName,
  paymentMethod,
  invoiceUrl,
  paymentId,
  currency = 'BRL',
  gracePeriod
}: PaymentDueTodayTemplateProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    if (!dateString || dateString === 'undefined' || dateString === 'null') {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(new Date());
    }

    // Se a data está no formato YYYY-MM-DD (apenas data), tratar como data local
    // para evitar problemas de timezone
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const [year, month, day] = dateString.split('-').map(Number);
      const date = new Date(year, month - 1, day); // month é 0-indexed
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(date);
    }

    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(new Date());
    }

    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  const dueDateFormatted = formatDate(dueDate);

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`Sua mensalidade vence HOJE - ${planName} - ${formatCurrency(amount)}`}
    >
      <EmailHeading level={1} color="#d97706">
        ⚠️ Vencimento Hoje
      </EmailHeading>

      <EmailText>
        Olá <strong>{studentName}</strong>,
      </EmailText>

      <EmailText>
        Sua mensalidade <strong>vence hoje</strong>! Para manter seus treinos em dia 
        e evitar qualquer interrupção, realize o pagamento o quanto antes.
      </EmailText>

      <EmailDivider color="#d97706" />

      {/* Detalhes do pagamento */}
      <div style={{
        backgroundColor: '#fffbeb',
        padding: '24px',
        borderRadius: '8px',
        border: '1px solid #fed7aa'
      }}>
        <EmailHeading level={3} color="#d97706">
          ⏰ Detalhes do Pagamento
        </EmailHeading>

        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color="#92400e">
            <strong>Plano:</strong> {planName}
          </EmailText>
          <EmailText variant="small" color="#92400e">
            <strong>Valor:</strong> {formatCurrency(amount)}
          </EmailText>
          <EmailText variant="small" color="#92400e">
            <strong>Vencimento:</strong> {dueDateFormatted} (HOJE)
          </EmailText>
          {paymentMethod && (
            <EmailText variant="small" color="#92400e">
              <strong>Forma de Pagamento:</strong> {paymentMethod}
            </EmailText>
          )}
        </div>

        <div style={{
          backgroundColor: '#fef3c7',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #fbbf24'
        }}>
          <EmailText variant="small" color="#92400e">
            ⚡ <strong>Ação Necessária:</strong> Realize o pagamento hoje para evitar atrasos 
            e manter acesso completo às atividades da academia.
          </EmailText>
          {gracePeriod && (
            <EmailText variant="small" color="#92400e">
              📅 Você tem {gracePeriod} dias de tolerância após o vencimento.
            </EmailText>
          )}
        </div>
      </div>

      <EmailDivider />

      {/* Botão de ação destacado */}
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        {paymentId && tenantSlug ? (
          <EmailButton href={`https://${tenantSlug}.${process.env.NEXT_PUBLIC_BASE_DOMAIN}/checkout/${paymentId}`} primaryColor="#d97706">
            Pagar Agora
          </EmailButton>
        ) : invoiceUrl ? (
          <EmailButton href={invoiceUrl} primaryColor="#d97706">
            Pagar Agora
          </EmailButton>
        ) : (
          <EmailButton href="#" primaryColor="#d97706">
            Realizar Pagamento
          </EmailButton>
        )}
      </div>

      {/* Informações de suporte */}
      <EmailText variant="small" color="#64748b">
        <strong>Precisa de ajuda?</strong> Se você já realizou o pagamento ou tem 
        alguma dúvida, entre em contato conosco imediatamente.
      </EmailText>

      <div style={{
        backgroundColor: '#f0f9ff',
        padding: '16px',
        borderRadius: '6px',
        border: '1px solid #93c5fd',
        marginTop: '24px'
      }}>
        <EmailText variant="small" color="#1e40af">
          💪 <strong>Mantenha o foco:</strong> Não deixe que questões administrativas 
          atrapalhem seus objetivos. Resolva hoje e continue evoluindo!
        </EmailText>
      </div>
    </BaseEmailTemplate>
  );
}
