/**
 * Exemplos de uso dos templates React Email
 * Demonstra como converter dados dos DEFAULT_TEMPLATES para React Email
 */

import { render } from '@react-email/render';
import {
  PaymentReminderTemplate,
  PaymentDueSoonTemplate,
  PaymentDueTodayTemplate,
  PaymentOverdueTemplate,
  WelcomeTemplate,
  EventInvitationTemplate,
  AdminAlertTemplate,
  convertToReactEmailTemplate
} from './index';

// Exemplo 1: Lembrete de pagamento básico
export async function renderPaymentReminderEmail() {
  const templateData = {
    academyName: 'Academia Guerreiros',
    academyLogo: 'https://example.com/logo.png',
    primaryColor: '#007291',
    studentName: '<PERSON>',
    amount: 150.00,
    dueDate: '2024-01-15',
    planName: 'Plano Mensal Jiu-Jitsu',
    paymentMethod: 'PIX',
    invoiceUrl: 'https://example.com/invoice/123',
    currency: 'BRL'
  };

  const html = await render(PaymentReminderTemplate(templateData));
  return html;
}

// Exemplo 2: Pagamento que vence em 3 dias
export async function renderPaymentDueSoonEmail() {
  const templateData = {
    academyName: 'Academia Guerreiros',
    studentName: 'Maria Santos',
    amount: 200.00,
    dueDate: '2024-01-18',
    planName: 'Plano Familiar',
    daysUntilDue: 3
  };

  const html = await render(PaymentDueSoonTemplate(templateData));
  return html;
}

// Exemplo 3: Pagamento que vence hoje
export async function renderPaymentDueTodayEmail() {
  const templateData = {
    academyName: 'Academia Guerreiros',
    studentName: 'Carlos Oliveira',
    amount: 120.00,
    dueDate: new Date().toISOString().split('T')[0],
    planName: 'Plano Estudante',
    gracePeriod: 5
  };

  const html = await render(PaymentDueTodayTemplate(templateData));
  return html;
}

// Exemplo 4: Pagamento em atraso (nível 2)
export async function renderPaymentOverdueEmail() {
  const templateData = {
    academyName: 'Academia Guerreiros',
    studentName: 'Ana Costa',
    amount: 180.00,
    dueDate: '2024-01-01',
    planName: 'Plano Premium',
    overdueDays: 10,
    overdueLevel: 2 as const,
    lateFee: 18.00,
    suspensionWarning: true
  };

  const html = await render(PaymentOverdueTemplate(templateData));
  return html;
}

// Exemplo 5: Boas-vindas para novo aluno
export async function renderWelcomeEmail() {
  const templateData = {
    academyName: 'Academia Guerreiros',
    academyLogo: 'https://example.com/logo.png',
    studentName: 'Pedro Almeida',
    planName: 'Plano Iniciante Muay Thai',
    startDate: '2024-01-20',
    enrollmentDate: '2024-01-15',
    instructorName: 'Professor Roberto',
    firstClassDate: '2024-01-22',
    academyAddress: 'Rua das Artes Marciais, 123 - Centro',
    academyPhone: '(11) 99999-9999',
    welcomeMessage: 'Bem-vindo à nossa família de guerreiros! Estamos ansiosos para vê-lo evoluir.',
    dashboardUrl: 'https://app.academia.com/dashboard'
  };

  const html = await render(WelcomeTemplate(templateData));
  return html;
}

// Exemplo 6: Convite para evento
export async function renderEventInvitationEmail() {
  const templateData = {
    academyName: 'Academia Guerreiros',
    eventName: 'Seminário de Defesa Pessoal',
    eventDescription: 'Um seminário especial com técnicas avançadas de defesa pessoal ministrado por especialistas renomados.',
    eventDate: '2024-02-10',
    eventTime: '14:00',
    eventLocation: 'Ginásio Principal da Academia',
    eventDuration: '4 horas',
    studentName: 'Lucia Fernandes',
    eventPrice: 50.00,
    registrationUrl: 'https://academia.com/events/seminario-defesa',
    registrationDeadline: '2024-02-05',
    maxParticipants: 30,
    currentParticipants: 22,
    requirements: [
      'Faixa branca ou superior',
      'Trazer kimono e proteções'
    ],
    benefits: [
      'Certificado de participação',
      'Material didático incluso',
      'Coffee break'
    ]
  };

  const html = await render(EventInvitationTemplate(templateData));
  return html;
}

// Exemplo 7: Alerta administrativo para atrasos
export async function renderAdminAlertEmail() {
  const templateData = {
    academyName: 'Academia Guerreiros',
    adminName: 'Sensei Carlos',
    alertType: 'overdue_payments' as const,
    alertTitle: 'Alerta: Pagamentos em Atraso Crítico',
    alertMessage: 'Temos um número significativo de alunos com pagamentos em atraso que requerem ação imediata.',
    overdueCount: 15,
    totalAtRisk: 2250.00,
    priority: 'high' as const,
    actionUrl: 'https://admin.academia.com/payments/overdue',
    actionLabel: 'Revisar Pagamentos em Atraso',
    additionalData: [
      { label: 'Atrasos Nível 1 (3-7 dias)', value: '8 alunos', highlight: false },
      { label: 'Atrasos Nível 2 (8-14 dias)', value: '5 alunos', highlight: true },
      { label: 'Atrasos Nível 3 (15+ dias)', value: '2 alunos', highlight: true },
      { label: 'Maior atraso', value: '23 dias', highlight: true }
    ]
  };

  const html = await render(AdminAlertTemplate(templateData));
  return html;
}

// Exemplo 8: Usando o conversor automático
export async function renderUsingConverter() {
  // Simula dados vindos dos DEFAULT_TEMPLATES
  const templateData = {
    academyName: 'Academia Guerreiros',
    studentName: 'Roberto Silva',
    amount: '175.50',
    dueDate: '2024-01-20',
    planName: 'Plano Avançado',
    overdueDays: '5'
  };

  // Converte automaticamente para React Email
  const { templateType, props } = convertToReactEmailTemplate(
    'payment',
    templateData,
    'payment_overdue_level_1'
  );

  console.log('Template selecionado:', templateType);
  console.log('Props geradas:', props);

  // Renderiza o template apropriado
  // Note: Em um caso real, você usaria um switch/case ou factory pattern
  if (templateType === 'payment-overdue') {
    const html = await render(PaymentOverdueTemplate(props as any));
    return html;
  }

  return null;
}

// Exemplo 9: Função utilitária para renderizar qualquer template
export async function renderEmailTemplate(
  notificationType: 'payment' | 'class' | 'enrollment' | 'event' | 'system',
  templateData: Record<string, any>,
  templateName?: string
): Promise<string> {
  const { templateType, props } = convertToReactEmailTemplate(
    notificationType,
    templateData,
    templateName
  );

  // Factory pattern para renderizar o template correto
  switch (templateType) {
    case 'payment-reminder':
      return await render(PaymentReminderTemplate(props as any));
    
    case 'payment-due-soon':
      return await render(PaymentDueSoonTemplate(props as any));
    
    case 'payment-due-today':
      return await render(PaymentDueTodayTemplate(props as any));
    
    case 'payment-overdue':
      return await render(PaymentOverdueTemplate(props as any));
    
    case 'welcome':
      return await render(WelcomeTemplate(props as any));
    
    case 'event-invitation':
      return await render(EventInvitationTemplate(props as any));
    
    case 'admin-alert':
      return await render(AdminAlertTemplate(props as any));
    
    default:
      throw new Error(`Template type ${templateType} not implemented`);
  }
}

// Exemplo de uso em um serviço de email
export class ReactEmailService {
  async sendPaymentReminder(studentData: any, paymentData: any) {
    const templateData = {
      ...studentData,
      ...paymentData,
      academyName: 'Academia Guerreiros'
    };

    const html = await renderEmailTemplate('payment', templateData);
    
    // Aqui você enviaria o email usando seu provedor preferido
    // await emailProvider.send({
    //   to: studentData.email,
    //   subject: 'Lembrete de Pagamento',
    //   html
    // });
    
    return html;
  }

  async sendWelcomeEmail(studentData: any, enrollmentData: any) {
    const templateData = {
      ...studentData,
      ...enrollmentData,
      academyName: 'Academia Guerreiros'
    };

    const html = await renderEmailTemplate('enrollment', templateData);
    
    return html;
  }
}
