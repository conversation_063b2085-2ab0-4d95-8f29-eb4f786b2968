/**
 * Tipos TypeScript para o sistema de notificações
 * Baseado no documento de planejamento do sistema de notificações
 */

// Tipos básicos de notificação
export type NotificationType =
  | 'payment'
  | 'class'
  | 'system'
  | 'enrollment'
  | 'enrollment_payment_due'
  | 'expense_due'
  | 'new_enrollment_notification'
  | 'event';

export type NotificationCategory =
  | 'reminder'
  | 'alert'
  | 'info'
  | 'success'
  | 'error';

export type NotificationPriority =
  | 'low'
  | 'medium'
  | 'high'
  | 'urgent';

export type NotificationStatus =
  | 'unread'
  | 'read'
  | 'archived'
  | 'deleted';

export type NotificationChannel =
  | 'in_app'
  | 'email'
  | 'whatsapp';

// Interface principal de notificação
export interface Notification {
  id: string;
  tenant_id: string;
  user_id: string;
  type: NotificationType;
  category: NotificationCategory;
  priority: NotificationPriority;
  title: string;
  message: string;
  data: Record<string, any>;
  status: NotificationStatus;
  channels: NotificationChannel[];
  scheduled_for?: string;
  expires_at?: string;
  created_at: string;
  updated_at?: string;
  read_at?: string;
}

// Interface para preferências de notificação
export interface NotificationPreferences {
  id: string;
  tenant_id: string;
  user_id: string;
  notification_type: NotificationType;
  in_app_enabled: boolean;
  email_enabled: boolean;
  whatsapp_enabled: boolean;
  quiet_hours_start?: string;
  quiet_hours_end?: string;
  timezone: string;
  created_at: string;
  updated_at?: string;
}

// Interface para criar notificação
export interface CreateNotificationData {
  user_id: string;
  type: NotificationType;
  category: NotificationCategory;
  priority?: NotificationPriority;
  title: string;
  message: string;
  data?: Record<string, any>;
  channels?: NotificationChannel[];
  scheduled_for?: string;
  expires_at?: string;
}

// Interface para templates de notificação
export interface NotificationTemplate {
  id: string;
  tenant_id?: string;
  type: NotificationType;
  channel: NotificationChannel;
  name: string;
  subject_template?: string;
  body_template: string;
  variables: Record<string, any>;
  is_active: boolean;
  is_default: boolean;
  version: number;
  parent_template_id?: string;
  created_at: string;
  updated_at?: string;
}

// Interface para variáveis de template
export interface TemplateVariable {
  id: string;
  template_type: NotificationType;
  variable_name: string;
  variable_key: string;
  description?: string;
  data_type: 'string' | 'number' | 'date' | 'boolean' | 'url';
  is_required: boolean;
  default_value?: string;
  example_value?: string;
  category?: string;
  created_at: string;
}

// Interface para preview de template
export interface TemplatePreviewData {
  template: NotificationTemplate;
  variables: Record<string, any>;
  rendered_subject?: string;
  rendered_body: string;
}

// Interfaces para filtros e paginação
export interface NotificationFilters {
  status?: NotificationStatus[];
  type?: NotificationType[];
  category?: NotificationCategory[];
  priority?: NotificationPriority[];
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface PaginatedNotifications {
  data: Notification[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Interfaces para criação e atualização de templates
export interface CreateTemplateData {
  type: NotificationType;
  channel: NotificationChannel;
  name: string;
  subject_template?: string;
  body_template: string;
  parent_template_id?: string;
}

export interface UpdateTemplateData {
  name?: string;
  subject_template?: string;
  body_template?: string;
  is_active?: boolean;
}

// Interface para validação de template
export interface TemplateValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  missingVariables: string[];
  unusedVariables: string[];
}

// Interfaces para preferências de notificação
export interface CreateNotificationPreferencesData {
  notification_type: NotificationType;
  in_app_enabled?: boolean;
  email_enabled?: boolean;
  whatsapp_enabled?: boolean;
  quiet_hours_start?: string;
  quiet_hours_end?: string;
  timezone?: string;
}

export interface UpdateNotificationPreferencesData {
  in_app_enabled?: boolean;
  email_enabled?: boolean;
  whatsapp_enabled?: boolean;
  quiet_hours_start?: string;
  quiet_hours_end?: string;
  timezone?: string;
}

// Tipos para respostas de API
export interface NotificationServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Tipos para estatísticas de notificação
export interface NotificationStats {
  total: number;
  unread: number;
  read: number;
  archived: number;
  byType: Record<NotificationType, number>;
  byCategory: Record<NotificationCategory, number>;
  byPriority: Record<NotificationPriority, number>;
}
