'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Bug, Loader2, Send, Calendar, CreditCard, Shield, CheckCircle } from 'lucide-react';
import {
  createTestNotification,
  createTestDispatcherForStudent,
  createTestClassReminderDispatcher,
  createTestPaymentReminderDispatcher,
  testNotificationPermissions,
  testPaymentReminderWithNewPermissions
} from '../actions/debug-actions';
import { toast } from 'sonner';

/**
 * Botão de debug para criar notificações de teste
 * Só aparece em ambiente de desenvolvimento
 */
export function DebugNotificationButton() {
  const [isLoading, setIsLoading] = useState(false);
  const [isDispatcherLoading, setIsDispatcherLoading] = useState(false);
  const [isClassReminderLoading, setIsClassReminderLoading] = useState(false);
  const [isPaymentReminderLoading, setIsPaymentReminderLoading] = useState(false);
  const [isPermissionTestLoading, setIsPermissionTestLoading] = useState(false);
  const [isNewPaymentTestLoading, setIsNewPaymentTestLoading] = useState(false);

  // ID do estudante para teste
  const STUDENT_TEST_ID = '8aecebc9-4145-4a21-ae45-4a714e08f7bf';

  // Só renderizar em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const handleCreateTestNotification = async () => {
    setIsLoading(true);
    
    try {
      const result = await createTestNotification();
      
      if (result.success) {
        toast.success(result.message || 'Notificação de teste criada!', {
          description: 'Verifique o painel de notificações para ver a nova notificação.',
          duration: 5000
        });
      } else {
        toast.error('Erro ao criar notificação', {
          description: result.error || 'Erro desconhecido',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Erro ao criar notificação de teste:', error);
      toast.error('Erro inesperado', {
        description: 'Ocorreu um erro inesperado ao criar a notificação de teste.',
        duration: 5000
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTestDispatcher = async () => {
    setIsDispatcherLoading(true);

    try {
      const result = await createTestDispatcherForStudent(STUDENT_TEST_ID);

      if (result.success) {
        toast.success(result.message || 'Dispatcher de teste criado!', {
          description: `Notificação enviada para o estudante ${STUDENT_TEST_ID}`,
          duration: 5000
        });
      } else {
        toast.error('Erro ao criar dispatcher', {
          description: result.error || 'Erro desconhecido',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Erro ao criar dispatcher de teste:', error);
      toast.error('Erro inesperado', {
        description: 'Ocorreu um erro inesperado ao criar o dispatcher de teste.',
        duration: 5000
      });
    } finally {
      setIsDispatcherLoading(false);
    }
  };

  const handleCreateTestClassReminder = async () => {
    setIsClassReminderLoading(true);

    try {
      const result = await createTestClassReminderDispatcher(STUDENT_TEST_ID);

      if (result.success) {
        toast.success(result.message || 'Lembrete de aula de teste criado!', {
          description: `Notificação de lembrete de aula enviada para o estudante ${STUDENT_TEST_ID}`,
          duration: 5000
        });
      } else {
        toast.error('Erro ao criar lembrete de aula', {
          description: result.error || 'Erro desconhecido',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Erro ao criar lembrete de aula de teste:', error);
      toast.error('Erro inesperado', {
        description: 'Ocorreu um erro inesperado ao criar o lembrete de aula de teste.',
        duration: 5000
      });
    } finally {
      setIsClassReminderLoading(false);
    }
  };

  const handleCreateTestPaymentReminder = async () => {
    setIsPaymentReminderLoading(true);

    try {
      const result = await createTestPaymentReminderDispatcher(STUDENT_TEST_ID);

      if (result.success) {
        toast.success(result.message || 'Lembrete de pagamento de teste criado!', {
          description: `Notificação de lembrete de pagamento enviada para o estudante ${STUDENT_TEST_ID}`,
          duration: 5000
        });
      } else {
        toast.error('Erro ao criar lembrete de pagamento', {
          description: result.error || 'Erro desconhecido',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Erro ao criar lembrete de pagamento de teste:', error);
      toast.error('Erro inesperado', {
        description: 'Ocorreu um erro inesperado ao criar o lembrete de pagamento de teste.',
        duration: 5000
      });
    } finally {
      setIsPaymentReminderLoading(false);
    }
  };

  const handleTestPermissions = async () => {
    setIsPermissionTestLoading(true);

    try {
      const result = await testNotificationPermissions(STUDENT_TEST_ID);

      if (result.success) {
        toast.success('Teste de permissões concluído!', {
          description: 'Verifique o console para ver os resultados detalhados.',
          duration: 5000
        });

        // Log detalhado no console para análise
        console.log('🔍 TESTE DE PERMISSÕES DE NOTIFICAÇÃO');
        console.log('=====================================');
        console.log(`Tenant ID: ${result.data?.tenantId}`);
        console.log(`Student ID: ${result.data?.studentUserId}`);
        console.log('');

        result.data?.results.forEach((test: any) => {
          const status = test.allowed ? '✅ PERMITIDO' : '❌ BLOQUEADO';
          console.log(`${test.type.toUpperCase()} via ${test.channel.toUpperCase()}: ${status}`);
          if (!test.allowed) {
            console.log(`   Motivo: ${test.reason}`);
          }
          console.log(`   Fonte: ${test.source}`);
          console.log('');
        });
      } else {
        toast.error('Erro ao testar permissões', {
          description: result.error || 'Erro desconhecido',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Erro ao testar permissões:', error);
      toast.error('Erro inesperado', {
        description: 'Ocorreu um erro inesperado ao testar permissões.',
        duration: 5000
      });
    } finally {
      setIsPermissionTestLoading(false);
    }
  };

  const handleTestNewPaymentPermissions = async () => {
    setIsNewPaymentTestLoading(true);

    try {
      const result = await testPaymentReminderWithNewPermissions(STUDENT_TEST_ID);

      if (result.success) {
        toast.success('Teste de pagamento com permissões concluído!', {
          description: 'Verifique o console para ver os resultados detalhados.',
          duration: 5000
        });

        // Log detalhado no console
        console.log('🧪 RESULTADO DO TESTE DE PAGAMENTO COM PERMISSÕES');
        console.log('=================================================');
        console.log('Permissões verificadas:', result.data?.permissions);
        console.log('Resultado do dispatch:', result.data?.dispatchResult);

        if (result.data?.permissions?.email?.allowed === false) {
          console.log('✅ EMAIL BLOQUEADO CORRETAMENTE!');
          console.log('Motivo:', result.data.permissions.email.reason);
        } else {
          console.log('❌ EMAIL NÃO FOI BLOQUEADO!');
        }
      } else {
        toast.error('Erro ao testar pagamento com permissões', {
          description: result.error || 'Erro desconhecido',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Erro ao testar pagamento com permissões:', error);
      toast.error('Erro inesperado', {
        description: 'Ocorreu um erro inesperado ao testar.',
        duration: 5000
      });
    } finally {
      setIsNewPaymentTestLoading(false);
    }
  };

  return (
    <div className="flex flex-wrap gap-2">
      <Button
        onClick={handleCreateTestNotification}
        disabled={isLoading}
        variant="outline"
        size="sm"
        className="flex items-center gap-2 bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100 dark:bg-orange-950/20 dark:border-orange-800 dark:text-orange-400 dark:hover:bg-orange-900/30"
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Bug className="h-4 w-4" />
        )}
        {isLoading ? 'Criando...' : 'Debug: Criar Notificação'}
      </Button>

      <Button
        onClick={handleCreateTestDispatcher}
        disabled={isDispatcherLoading}
        variant="outline"
        size="sm"
        className="flex items-center gap-2 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 dark:bg-blue-950/20 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/30"
      >
        {isDispatcherLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Send className="h-4 w-4" />
        )}
        {isDispatcherLoading ? 'Enviando...' : 'Debug: Dispatcher Teste'}
      </Button>

      <Button
        onClick={handleCreateTestClassReminder}
        disabled={isClassReminderLoading}
        variant="outline"
        size="sm"
        className="flex items-center gap-2 bg-green-50 border-green-200 text-green-700 hover:bg-green-100 dark:bg-green-950/20 dark:border-green-800 dark:text-green-400 dark:hover:bg-green-900/30"
      >
        {isClassReminderLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Calendar className="h-4 w-4" />
        )}
        {isClassReminderLoading ? 'Enviando...' : 'Debug: Lembrete Aula'}
      </Button>

      <Button
        onClick={handleCreateTestPaymentReminder}
        disabled={isPaymentReminderLoading}
        variant="outline"
        size="sm"
        className="flex items-center gap-2 bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100 dark:bg-purple-950/20 dark:border-purple-800 dark:text-purple-400 dark:hover:bg-purple-900/30"
      >
        {isPaymentReminderLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <CreditCard className="h-4 w-4" />
        )}
        {isPaymentReminderLoading ? 'Enviando...' : 'Debug: Lembrete Pagamento'}
      </Button>

      <Button
        onClick={handleTestPermissions}
        disabled={isPermissionTestLoading}
        variant="outline"
        size="sm"
        className="flex items-center gap-2 bg-red-50 border-red-200 text-red-700 hover:bg-red-100 dark:bg-red-950/20 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/30"
      >
        {isPermissionTestLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Shield className="h-4 w-4" />
        )}
        {isPermissionTestLoading ? 'Testando...' : 'Debug: Testar Permissões'}
      </Button>

      <Button
        onClick={handleTestNewPaymentPermissions}
        disabled={isNewPaymentTestLoading}
        variant="outline"
        size="sm"
        className="flex items-center gap-2 bg-emerald-50 border-emerald-200 text-emerald-700 hover:bg-emerald-100 dark:bg-emerald-950/20 dark:border-emerald-800 dark:text-emerald-400 dark:hover:bg-emerald-900/30"
      >
        {isNewPaymentTestLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <CheckCircle className="h-4 w-4" />
        )}
        {isNewPaymentTestLoading ? 'Testando...' : 'Debug: Teste Pagamento + Permissões'}
      </Button>
    </div>
  );
}
