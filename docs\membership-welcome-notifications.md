# Notificações de Boas-vindas para Matrículas

## Visão Geral

Este documento descreve a implementação das notificações automáticas de boas-vindas que são enviadas aos alunos quando uma nova matrícula é criada no sistema.

## Funcionalidade

Quando um aluno é matriculado em um plano, o sistema automaticamente:

1. **Cria a matrícula** no banco de dados
2. **Gera os pagamentos** relacionados ao plano
3. **Envia notificação de boas-vindas** para o aluno via:
   - Notificação in-app
   - Email com template personalizado

## Implementação

### Arquivo Principal
- `src/app/(dashboard)/academia/actions/membership-actions.ts`

### Função Responsável
```typescript
async function sendWelcomeNotification(
  tenantId: string,
  studentId: string,
  planId: string,
  membershipData: any
): Promise<void>
```

### Fluxo de Execução

1. **Busca dados do estudante**
   - Nome, email, data de nascimento
   - Dados do usuário associado

2. **Busca dados do plano**
   - Título e descrição do plano

3. **Busca dados da academia**
   - Nome da academia
   - Configurações personalizadas

4. **Determina destinatário**
   - Se menor de idade: usa email do responsável
   - Se maior de idade: usa email do próprio aluno

5. **Envia notificação**
   - Tipo: `enrollment`
   - Template: `enrollment_welcome`
   - Canais: `in_app` e `email`

### Template de Email

O sistema utiliza o template `WelcomeTemplate` localizado em:
- `src/services/notifications/channels/email/templates/welcome-template.tsx`

#### Dados Enviados para o Template

```typescript
{
  academyName: string,
  studentName: string,
  planName: string,
  startDate: string,
  enrollmentDate: string,
  welcomeMessage?: string,
  dashboardUrl?: string
}
```

### Pontos de Integração

A notificação é disparada em dois pontos da função `createMembership`:

1. **Versão Direta** (quando `tenantId` é fornecido):
   ```typescript
   await sendWelcomeNotification(
     tenantId,
     validatedData.alunoId,
     validatedData.planoId,
     membershipData
   );
   ```

2. **Versão RPC** (fallback para dashboard):
   ```typescript
   if (userTenantData?.tenant_id) {
     await sendWelcomeNotification(
       userTenantData.tenant_id,
       validatedData.alunoId,
       validatedData.planoId,
       membershipData
     );
   }
   ```

## Tratamento de Erros

- **Falhas na notificação não afetam a criação da matrícula**
- Erros são logados no console para debugging
- Sistema continua funcionando mesmo se o serviço de notificações falhar

## Configurações

### Permissões de Notificação

As notificações respeitam as configurações de permissão do tenant:
- Configuradas em `tenant_notification_settings`
- Tipo `enrollment` deve estar habilitado para email/in_app

### Personalização

- **Mensagem de boas-vindas**: Configurável via `tenant.settings.welcome_message`
- **URL do dashboard**: Baseada em `NEXT_PUBLIC_APP_URL`
- **Cores da academia**: Personalizáveis no template

## Logs e Debugging

Para acompanhar o funcionamento:

```javascript
// Logs de sucesso
console.log('Notificação de boas-vindas enviada com sucesso:', {
  notificationId: result.notificationId,
  studentName: studentData.name,
  planName: planData.title
});

// Logs de erro
console.warn('Falha ao enviar notificação de boas-vindas:', result.errors);
```

## Dependências

- `@/services/notifications` - Sistema de notificações
- `@/services/supabase/server` - Cliente Supabase
- Templates React Email para formatação

## Próximos Passos

1. **Testes automatizados** para validar o fluxo completo
2. **Métricas de entrega** para acompanhar taxa de sucesso
3. **Templates personalizáveis** por academia
4. **Notificações WhatsApp** (futura implementação)

## Troubleshooting

### Notificação não enviada

1. Verificar se `tenant_notification_settings` existe
2. Confirmar se tipo `enrollment` está habilitado
3. Validar dados do estudante (email válido)
4. Verificar logs do console para erros específicos

### Email não recebido

1. Verificar configurações SMTP do tenant
2. Confirmar se email não está em spam
3. Validar template de email
4. Verificar permissões de canal de email
