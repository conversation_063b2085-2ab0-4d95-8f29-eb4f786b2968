'use server';

import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { revalidatePath } from 'next/cache';
import { createClient } from '@/services/supabase/server';
import { paymentService } from '@/services/billing';
import { NotificationDispatcher } from '@/services/notifications';
import type { NotificationChannel } from '@/services/notifications/types/notification-types';
import {
  criarMembershipSchema,
  atualizarStatusMembershipSchema,
  pausarMembershipSchema,
  cancelarMembershipSchema,
  buscarMembershipsAtivasSchema,
  calcularPrecoSchema,
  processarCobrancaMembershipSchema,
  overviewMembershipSchema,
  estatisticasMembershipSchema,
  type CriarMembershipData,
  type AtualizarStatusMembershipData,
  type PausarMembershipData,
  type CancelarMembershipData,
  type BuscarMembershipsAtivasData,
  type CalcularPrecoData,
  type ProcessarCobrancaMembershipData,
  type OverviewMembershipData,
  type EstatisticasMembershipData,
} from '@/schemas/membership-schemas';

interface ActionResult<T = unknown> {
  success: boolean;
  data?: T;
  errors?: Record<string, string>;
}

/**
 * Função auxiliar para enviar notificação de boas-vindas ao aluno
 * Busca dados necessários e dispara notificação via email e in-app
 */
async function sendWelcomeNotification(
  tenantId: string,
  studentId: string,
  planId: string,
  membershipData: any
): Promise<void> {
  console.log('🔔 Iniciando envio de notificação de boas-vindas:', {
    tenantId,
    studentId,
    planId,
    membershipId: membershipData?.id
  });

  try {
    const supabase = await createClient();
    const dispatcher = new NotificationDispatcher();

    // Buscar dados do estudante
    const { data: studentData, error: studentError } = await supabase
      .from('students')
      .select(`
        id,
        birth_date,
        user_id,
        users!user_id(
          id,
          email,
          first_name,
          last_name
        )
      `)
      .eq('id', studentId)
      .eq('tenant_id', tenantId)
      .single();

    if (studentError || !studentData) {
      console.warn('Erro ao buscar dados do estudante para notificação:', studentError?.message);
      return;
    }

    // Buscar dados do plano
    const { data: planData, error: planError } = await supabase
      .from('plans')
      .select('id, title')
      .eq('id', planId)
      .eq('tenant_id', tenantId)
      .single();

    if (planError || !planData) {
      console.warn('Erro ao buscar dados do plano para notificação:', planError?.message);
      return;
    }

    // Buscar dados da academia
    const { data: tenantData, error: tenantError } = await supabase
      .from('tenants')
      .select('id, name, slug, settings')
      .eq('id', tenantId)
      .single();

    if (tenantError || !tenantData) {
      console.warn('Erro ao buscar dados da academia para notificação:', tenantError?.message);
      return;
    }

    console.log('📊 Dados do estudante obtidos:', {
      studentId: studentData.id,
      userId: studentData.user_id,
      hasUserData: !!studentData.users
    });

    // Acessar dados do usuário (relacionamento retorna array, pegar o primeiro)
    const userData = Array.isArray(studentData.users) ? studentData.users[0] : studentData.users;

    if (!userData) {
      console.warn('❌ Dados do usuário não encontrados para o estudante:', studentId);
      return;
    }

    console.log('👤 Dados do usuário obtidos:', {
      userId: userData.id,
      email: userData.email,
      firstName: userData.first_name,
      lastName: userData.last_name
    });

    // Para menores, usar email do responsável se disponível, senão usar email do usuário
    const recipientEmail = userData.email;
    const studentName = `${userData.first_name} ${userData.last_name || ''}`.trim();

    // Preparar dados para a notificação
    const welcomeData = {
      tenantId,
      userId: userData.id,
      type: 'enrollment' as const,
      category: 'info' as const,
      priority: 'medium' as const,
      title: `Bem-vindo(a) à ${tenantData.name}!`,
      message: `Sua matrícula no plano ${planData.title} foi realizada com sucesso. Bem-vindo(a) à nossa academia!`,
      channels: ['in_app', 'email'] as NotificationChannel[],
      templateId: 'enrollment_welcome',
      recipientEmail,
      variables: {
        academyName: tenantData.name,
        tenantSlug: tenantData.slug,
        studentName: studentName,
        planName: planData.title,
        startDate: membershipData.start_date,
        enrollmentDate: membershipData.created_at || new Date().toISOString(),
        welcomeMessage: tenantData.settings?.welcome_message || undefined
      }
    };

    console.log('📧 Preparando para enviar notificação:', {
      tenantId: welcomeData.tenantId,
      userId: welcomeData.userId,
      type: welcomeData.type,
      channels: welcomeData.channels,
      templateId: welcomeData.templateId,
      recipientEmail: welcomeData.recipientEmail
    });

    // Enviar notificação
    const result = await dispatcher.dispatch(welcomeData);

    if (result.success) {
      console.log('✅ Notificação de boas-vindas enviada com sucesso:', {
        notificationId: result.notificationId,
        studentName: studentName,
        planName: planData.title
      });
    } else {
      console.warn('❌ Falha ao enviar notificação de boas-vindas:', result.errors);
    }

  } catch (error) {
    console.error('Erro inesperado ao enviar notificação de boas-vindas:', error);
    // Não propagar o erro para não afetar a criação da matrícula
  }
}

/**
 * Função auxiliar para criar pagamentos de membership
 * Unifica a lógica de criação de pagamentos recorrentes e one-time
 */
async function createMembershipPayments(
  membershipId: string,
  pricingConfig: any,
  context: string = 'direct'
): Promise<any> {
  try {
    const isRecurring = pricingConfig?.type === 'recurring';

    if (isRecurring) {
      console.log(`Criando pagamentos recorrentes para membership (${context}):`, membershipId);

      const result = await paymentService.createRecurringPayments({
        membershipId,
        paymentsCount: 1
      });

      if (result.success) {
        console.log(`Pagamentos recorrentes criados com sucesso (${context}):`, result.data);
      } else {
        console.warn(`Não foi possível criar pagamentos recorrentes (${context}):`, result.error);
      }

      return result;
    } else {
      console.log(`Tentando criar pagamento inicial para membership (${context}):`, membershipId);

      const result = await paymentService.createInitialPayment({
        membershipId
      });

      if (result.success) {
        console.log(`Pagamento inicial criado com sucesso (${context}):`, result.data);
      } else {
        console.warn(`Não foi possível criar pagamento inicial (${context}):`, result.error);
      }

      return result;
    }
  } catch (error) {
    console.error(`Erro ao criar pagamentos (${context}):`, error);
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

export async function createMembership(data: unknown, tenantId?: string): Promise<ActionResult> {
  try {
    const result = criarMembershipSchema.safeParse(data);

    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Se tenantId foi fornecido, usar lógica direta ao invés da RPC
    if (tenantId) {
      // Validar se o estudante pertence ao tenant
      const { data: studentData, error: studentError } = await supabase
        .from('students')
        .select('id')
        .eq('id', validatedData.alunoId)
        .eq('tenant_id', tenantId)
        .single();

      if (studentError || !studentData) {
        return {
          success: false,
          errors: { _form: 'Estudante não encontrado ou acesso negado' },
        };
      }

      // Validar se o plano existe e está ativo
      const { data: planData, error: planError } = await supabase
        .from('plans')
        .select('id, title, status, pricing_config, duration_config')
        .eq('id', validatedData.planoId)
        .eq('tenant_id', tenantId)
        .eq('status', 'active')
        .single();

      if (planError || !planData) {
        return {
          success: false,
          errors: { _form: 'Plano não encontrado ou não está ativo' },
        };
      }

      // Verificar política de múltiplas matrículas
      const { data: tenantSettings } = await supabase
        .from('tenants')
        .select('settings')
        .eq('id', tenantId)
        .single();

      const allowMultiple = tenantSettings?.settings?.allow_multiple_memberships || false;

      if (!allowMultiple) {
        const { data: activeMemberships, error: activeError } = await supabase
          .from('memberships')
          .select('id')
          .eq('student_id', validatedData.alunoId)
          .eq('tenant_id', tenantId)
          .eq('status', 'active');

        if (activeError) {
          return {
            success: false,
            errors: { _form: 'Erro ao verificar matrículas existentes' },
          };
        }

        if (activeMemberships && activeMemberships.length > 0) {
          return {
            success: false,
            errors: { _form: 'Estudante já possui uma matrícula ativa' },
          };
        }
      }

      // Calcular datas
      const startDate = validatedData.dataInicio || new Date().toISOString().split('T')[0];
      let endDate = null;

      const durationConfig = planData.duration_config || {};
      const pricingConfig = planData.pricing_config || {};

      // Calcular end_date baseado na configuração de duração
      if (durationConfig.type === 'limited') {
        const startDateObj = new Date(startDate);
        if (durationConfig.unit === 'months') {
          startDateObj.setMonth(startDateObj.getMonth() + (durationConfig.value || 1));
        } else {
          startDateObj.setDate(startDateObj.getDate() + (durationConfig.value || 30));
        }
        endDate = startDateObj.toISOString().split('T')[0];
      } else if (durationConfig.type === 'specific') {
        endDate = durationConfig.end_date;
      }

      // NOTE: next_billing_date is no longer used as we now use payments table as source of truth
      // The billing logic is now handled by the payment creation functions

      // Criar a matrícula
      const { data: membershipData, error: createError } = await supabase
        .from('memberships')
        .insert({
          tenant_id: tenantId,
          student_id: validatedData.alunoId,
          plan_id: validatedData.planoId,
          status: 'active',
          start_date: startDate,
          end_date: endDate,
          // NOTE: next_billing_date is no longer used as we now use payments table as source of truth
          metadata: validatedData.metadata || {}
        })
        .select()
        .single();

      if (createError) {
        return {
          success: false,
          errors: { _form: `Erro ao criar matrícula: ${createError.message}` },
        };
      }

      // Criar pagamentos baseado no tipo do plano
      const paymentResult = await createMembershipPayments(
        membershipData.id,
        pricingConfig,
        'direct'
      );

      // Enviar notificação de boas-vindas ao aluno
      await sendWelcomeNotification(
        tenantId,
        validatedData.alunoId,
        validatedData.planoId,
        membershipData
      );

      revalidatePath('/alunos');
      revalidatePath('/academia');
      revalidatePath('/financeiro');

      return {
        success: true,
        data: {
          success: true,
          membership_id: membershipData.id,
          end_date: endDate,
          message: 'Matrícula criada com sucesso',
          payment_result: paymentResult?.success ? {
            created: true,
            payment_id: paymentResult.data?.payment_id || paymentResult.data?.payments?.[0]?.payment_id,
            amount: paymentResult.data?.amount || paymentResult.data?.total_amount,
            message: paymentResult.message,
            type: pricingConfig.type === 'recurring' ? 'recurring' : 'initial'
          } : {
            created: false,
            reason: paymentResult?.error || 'Pagamentos não configurados ou erro na criação'
          }
        },
      };
    }

    // Fallback para RPC quando tenantId não é fornecido (contexto de dashboard)
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' },
      };
    }

    // Chamar função RPC create_membership (sem p_tenant_id)
    const { data: membershipData, error } = await supabase.rpc('create_membership', {
      p_student_id: validatedData.alunoId,
      p_plan_id: validatedData.planoId,
      p_start_date: validatedData.dataInicio,
      p_metadata: validatedData.metadata,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao criar matrícula: ${error.message}` },
      };
    }

    // Buscar informações do plano para determinar tipo de pagamento
    const { data: planData } = await supabase
      .from('plans')
      .select('pricing_config')
      .eq('id', validatedData.planoId)
      .single();

    // Criar pagamentos baseado no tipo do plano
    let paymentResult = null;
    if (membershipData?.membership_id) {
      paymentResult = await createMembershipPayments(
        membershipData.membership_id,
        planData?.pricing_config,
        'RPC'
      );

      // Enviar notificação de boas-vindas ao aluno (versão RPC)
      // Buscar tenantId do usuário atual
      const { data: userTenantData } = await supabase
        .from('user_tenants')
        .select('tenant_id')
        .eq('user_id', user.id)
        .single();

      if (userTenantData?.tenant_id) {
        await sendWelcomeNotification(
          userTenantData.tenant_id,
          validatedData.alunoId,
          validatedData.planoId,
          membershipData
        );
      }
    }

    revalidatePath('/alunos');
    revalidatePath('/academia');
    revalidatePath('/financeiro');

    return {
      success: true,
      data: {
        ...membershipData,
        payment_result: paymentResult?.success ? {
          created: true,
          payment_id: paymentResult.data?.payment_id || paymentResult.data?.payments?.[0]?.payment_id,
          amount: paymentResult.data?.amount || paymentResult.data?.total_amount,
          message: paymentResult.message,
          type: planData?.pricing_config?.type === 'recurring' ? 'recurring' : 'initial'
        } : {
          created: false,
          reason: paymentResult?.error || 'Pagamentos não configurados ou erro na criação'
        }
      },
    };
  } catch (error) {
    console.error('Erro ao criar matrícula:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function updateMembershipStatus(data: unknown, tenantId?: string): Promise<ActionResult> {
  try {
    const result = atualizarStatusMembershipSchema.safeParse(data);

    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Se tenantId foi fornecido, usar consulta direta ao invés da RPC
    if (tenantId) {
      // Primeiro, verificar se a membership existe e pertence ao tenant
      const { data: membershipData, error: membershipError } = await supabase
        .from('memberships')
        .select('id, status')
        .eq('id', validatedData.membershipId)
        .eq('tenant_id', tenantId)
        .single();

      if (membershipError || !membershipData) {
        return {
          success: false,
          errors: { _form: 'Matrícula não encontrada ou acesso negado' },
        };
      }

      const oldStatus = membershipData.status;

      // Não atualizar se o status é o mesmo
      if (oldStatus === validatedData.novoStatus) {
        return {
          success: true,
          data: {
            old_status: oldStatus,
            new_status: validatedData.novoStatus,
            message: `Status já está definido como ${validatedData.novoStatus}`
          },
        };
      }

      // Atualizar o status
      const { error: updateError } = await supabase
        .from('memberships')
        .update({
          status: validatedData.novoStatus,
          canceled_at: validatedData.novoStatus === 'canceled' ? new Date().toISOString() : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', validatedData.membershipId);

      if (updateError) {
        return {
          success: false,
          errors: { _form: `Erro ao atualizar status da matrícula: ${updateError.message}` },
        };
      }

      // Criar log de mudança de status manualmente
      await supabase
        .from('membership_status_logs')
        .insert({
          tenant_id: tenantId,
          membership_id: validatedData.membershipId,
          old_status: oldStatus,
          new_status: validatedData.novoStatus,
          change_reason: validatedData.motivo || 'Status updated via API'
        });

      revalidatePath('/alunos');
      revalidatePath('/academia');

      return {
        success: true,
        data: {
          old_status: oldStatus,
          new_status: validatedData.novoStatus,
          message: 'Status da matrícula atualizado com sucesso'
        },
      };
    }

    // Fallback para RPC quando tenantId não é fornecido (contexto de dashboard)
    const { data: statusData, error } = await supabase.rpc('update_membership_status', {
      p_membership_id: validatedData.membershipId,
      p_new_status: validatedData.novoStatus,
      p_reason: validatedData.motivo,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao atualizar status da matrícula: ${error.message}` },
      };
    }

    revalidatePath('/alunos');
    revalidatePath('/academia');

    return {
      success: true,
      data: statusData,
    };
  } catch (error) {
    console.error('Erro ao atualizar status da matrícula:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function pauseMembership(data: unknown): Promise<ActionResult> {
  try {
    const result = pausarMembershipSchema.safeParse(data);

    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Chamar função RPC pause_membership
    const { data: pauseData, error } = await supabase.rpc('pause_membership', {
      p_membership_id: validatedData.membershipId,
      p_reason: validatedData.motivo,
      p_metadata: validatedData.metadata,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao pausar matrícula: ${error.message}` },
      };
    }

    revalidatePath('/alunos');
    revalidatePath('/academia');

    return {
      success: true,
      data: pauseData,
    };
  } catch (error) {
    console.error('Erro ao pausar matrícula:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function cancelMembership(data: unknown, tenantId?: string): Promise<ActionResult> {
  try {
    const result = cancelarMembershipSchema.safeParse(data);

    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Se tenantId foi fornecido, usar lógica direta ao invés da RPC
    if (tenantId) {
      // Primeiro, verificar se a membership existe e pertence ao tenant
      const { data: membershipData, error: membershipError } = await supabase
        .from('memberships')
        .select(`
          id,
          status,
          student_id,
          plans!inner(
            id,
            title,
            duration_config
          )
        `)
        .eq('id', validatedData.membershipId)
        .eq('tenant_id', tenantId)
        .single();

      if (membershipError || !membershipData) {
        return {
          success: false,
          errors: { _form: 'Matrícula não encontrada ou acesso negado' },
        };
      }

      // Verificar se a matrícula pode ser cancelada
      if (membershipData.status === 'canceled') {
        return {
          success: false,
          errors: { _form: 'Matrícula já está cancelada' },
        };
      }

      if (membershipData.status === 'expired') {
        return {
          success: false,
          errors: { _form: 'Não é possível cancelar matrícula expirada' },
        };
      }

      const oldStatus = membershipData.status;

      // 1. Atualizar status da matrícula para 'canceled'
      const { error: updateError } = await supabase
        .from('memberships')
        .update({
          status: 'canceled',
          canceled_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', validatedData.membershipId);

      if (updateError) {
        return {
          success: false,
          errors: { _form: `Erro ao cancelar matrícula: ${updateError.message}` },
        };
      }

      // 2. Cancelar todos os pagamentos PENDENTES relacionados à matrícula usando RPC
      let paymentCancellationResult = null;
      try {
        const { data: cancelResult, error: cancelError } = await supabase.rpc('cancel_membership_payments', {
          p_membership_id: validatedData.membershipId,
          p_reason: validatedData.motivo || 'Matrícula cancelada'
        });

        if (cancelError) {
          console.warn('Erro ao cancelar pagamentos pendentes via RPC:', cancelError.message);
          paymentCancellationResult = { success: false, error: cancelError.message };
        } else if (cancelResult?.success) {
          console.log('Pagamentos cancelados com sucesso:', cancelResult.data);
          paymentCancellationResult = { success: true, data: cancelResult.data };
        } else {
          console.warn('RPC de cancelamento retornou erro:', cancelResult?.error);
          paymentCancellationResult = { success: false, error: cancelResult?.error || 'Erro desconhecido no cancelamento de pagamentos' };
        }
      } catch (error) {
        console.warn('Erro ao cancelar pagamentos pendentes:', error);
        paymentCancellationResult = { success: false, error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` };
        // Não falhar o cancelamento por causa dos pagamentos
      }

      // 3. Aplicar taxa de cancelamento se configurada e solicitada
      let cancellationFeeResult = null;
      if (validatedData.aplicarTaxaCancelamento) {
        try {
          const { data: feeData, error: feeError } = await supabase.rpc('create_cancellation_fee_payment', {
            p_membership_id: validatedData.membershipId,
            p_reason: validatedData.motivo || 'Cancelamento de matrícula'
          });

          if (feeError) {
            console.warn('Erro ao criar taxa de cancelamento:', feeError.message);
            cancellationFeeResult = {
              success: false,
              error: feeError.message
            };
          } else {
            cancellationFeeResult = feeData;
          }
        } catch (feeError) {
          console.warn('Erro ao processar taxa de cancelamento:', feeError);
          cancellationFeeResult = {
            success: false,
            error: 'Erro ao processar taxa de cancelamento'
          };
        }
      }

      // 4. Criar log de mudança de status
      await supabase
        .from('membership_status_logs')
        .insert({
          tenant_id: tenantId,
          membership_id: validatedData.membershipId,
          old_status: oldStatus,
          new_status: 'canceled',
          change_reason: validatedData.motivo || 'Matrícula cancelada'
        });

      revalidatePath('/alunos');
      revalidatePath('/academia');
      revalidatePath('/financeiro');

      return {
        success: true,
        data: {
          old_status: oldStatus,
          new_status: 'canceled',
          message: 'Matrícula cancelada com sucesso',
          payments_canceled: paymentCancellationResult?.success ? {
            canceled: true,
            count: paymentCancellationResult.data?.canceled_count || 0,
            message: `${paymentCancellationResult.data?.canceled_count || 0} pagamentos pendentes cancelados`
          } : {
            canceled: false,
            reason: paymentCancellationResult?.error || 'Erro no cancelamento de pagamentos'
          },
          cancellation_fee: cancellationFeeResult?.success ? {
            applied: true,
            amount: cancellationFeeResult.data?.amount || 0,
            payment_id: cancellationFeeResult.data?.payment_id
          } : {
            applied: false,
            reason: cancellationFeeResult?.error || 'Taxa não configurada ou erro na aplicação'
          }
        },
      };
    }

    // Fallback para RPC quando tenantId não é fornecido (contexto de dashboard)
    // Como não existe RPC específica para cancelamento, usar updateMembershipStatus
    const cancelResult = await updateMembershipStatus({
      membershipId: validatedData.membershipId,
      novoStatus: 'canceled',
      motivo: validatedData.motivo
    });

    if (!cancelResult.success) {
      return cancelResult;
    }

    // Cancelar pagamentos pendentes via query direta
    const { error: paymentsError } = await supabase
      .from('payments')
      .update({
        status: 'canceled',
        updated_at: new Date().toISOString()
      })
      .eq('membership_id', validatedData.membershipId)
      .eq('status', 'pending');

    if (paymentsError) {
      console.warn('Erro ao cancelar pagamentos pendentes:', paymentsError.message);
    }

    // Aplicar taxa de cancelamento se solicitada
    let cancellationFeeResult = null;
    if (validatedData.aplicarTaxaCancelamento) {
      try {
        const { data: feeData, error: feeError } = await supabase.rpc('create_cancellation_fee_payment', {
          p_membership_id: validatedData.membershipId,
          p_reason: validatedData.motivo || 'Cancelamento de matrícula'
        });

        if (feeError) {
          console.warn('Erro ao criar taxa de cancelamento:', feeError.message);
          cancellationFeeResult = {
            success: false,
            error: feeError.message
          };
        } else {
          cancellationFeeResult = feeData;
        }
      } catch (feeError) {
        console.warn('Erro ao processar taxa de cancelamento:', feeError);
        cancellationFeeResult = {
          success: false,
          error: 'Erro ao processar taxa de cancelamento'
        };
      }
    }

    revalidatePath('/alunos');
    revalidatePath('/academia');
    revalidatePath('/financeiro');

    return {
      success: true,
      data: {
        old_status: (cancelResult.data as any)?.old_status || 'active',
        new_status: 'canceled',
        message: 'Matrícula cancelada com sucesso',
        cancellation_fee: cancellationFeeResult?.success ? {
          applied: true,
          amount: cancellationFeeResult.data?.amount || 0,
          payment_id: cancellationFeeResult.data?.payment_id
        } : {
          applied: false,
          reason: cancellationFeeResult?.error || 'Taxa não configurada ou erro na aplicação'
        }
      },
    };

  } catch (error) {
    console.error('Erro ao cancelar matrícula:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function getStudentActiveMemberships(data: unknown, tenantId?: string): Promise<ActionResult> {
  try {
    const result = buscarMembershipsAtivasSchema.safeParse(data);

    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Se tenantId foi fornecido, usar consulta direta ao invés da RPC
    if (tenantId) {
      const { data: membershipsData, error } = await supabase
        .from('memberships')
        .select(`
          id,
          status,
          start_date,
          end_date,
          created_at,
          updated_at,
          plans!inner(
            id,
            title,
            plan_type,
            version,
            pricing_config,
            duration_config,
            access_config
          )
        `)
        .eq('student_id', validatedData.alunoId)
        .eq('tenant_id', tenantId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) {
        return {
          success: false,
          errors: { _form: `Erro ao buscar matrículas do estudante: ${error.message}` },
        };
      }

      // Transformar dados para o formato esperado (similar ao que a RPC retorna)
      const formattedData = membershipsData?.map(membership => ({
        id: membership.id,
        status: membership.status,
        start_date: membership.start_date,
        end_date: membership.end_date,
        plan: membership.plans,
        created_at: membership.created_at,
        updated_at: membership.updated_at
      })) || [];

      return {
        success: true,
        data: formattedData,
      };
    }

    // Fallback para RPC quando tenantId não é fornecido (contexto de dashboard)
    const { data: membershipsData, error } = await supabase.rpc('get_student_active_memberships', {
      p_student_id: validatedData.alunoId,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao buscar matrículas do estudante: ${error.message}` },
      };
    }

    return {
      success: true,
      data: membershipsData,
    };
  } catch (error) {
    console.error('Erro ao buscar matrículas do estudante:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function calculatePrice(data: unknown): Promise<ActionResult> {
  try {
    const result = calcularPrecoSchema.safeParse(data);

    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Chamar função RPC calculate_price
    const { data: priceData, error } = await supabase.rpc('calculate_price', {
      p_plan_id: validatedData.planoId,
      p_calculation_date: validatedData.dataCalculo,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao calcular preço: ${error.message}` },
      };
    }

    return {
      success: true,
      data: priceData,
    };
  } catch (error) {
    console.error('Erro ao calcular preço:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function processMembershipBilling(data: unknown): Promise<ActionResult> {
  try {
    const result = processarCobrancaMembershipSchema.safeParse(data);

    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Chamar função RPC process_membership_billing
    const { data: billingData, error } = await supabase.rpc('process_membership_billing', {
      p_membership_id: validatedData.membershipId,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao processar cobrança: ${error.message}` },
      };
    }

    revalidatePath('/financeiro');

    return {
      success: true,
      data: billingData,
    };
  } catch (error) {
    console.error('Erro ao processar cobrança:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function getMembershipOverview(data: unknown): Promise<ActionResult> {
  try {
    const result = overviewMembershipSchema.safeParse(data);

    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Chamar função RPC membership_overview
    const { data: overviewData, error } = await supabase.rpc('membership_overview', {
      p_start_date: validatedData.dataInicio,
      p_end_date: validatedData.dataFim,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao buscar visão geral das matrículas: ${error.message}` },
      };
    }

    return {
      success: true,
      data: overviewData,
    };
  } catch (error) {
    console.error('Erro ao buscar visão geral das matrículas:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function getMembershipStatistics(data: unknown): Promise<ActionResult> {
  try {
    const result = estatisticasMembershipSchema.safeParse(data);

    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>,
      };
    }

    const validatedData = result.data;
    const supabase = await createClient();

    // Chamar função RPC get_membership_statistics
    const { data: statisticsData, error } = await supabase.rpc('get_membership_statistics', {
      p_start_date: validatedData.dataInicio,
      p_end_date: validatedData.dataFim,
    });

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao buscar estatísticas das matrículas: ${error.message}` },
      };
    }

    return {
      success: true,
      data: statisticsData,
    };
  } catch (error) {
    console.error('Erro ao buscar estatísticas das matrículas:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

export async function getPlanUsageAnalytics(): Promise<ActionResult> {
  try {
    const supabase = await createClient();

    // Chamar função RPC get_plan_usage_analytics
    const { data: analyticsData, error } = await supabase.rpc('get_plan_usage_analytics');

    if (error) {
      return {
        success: false,
        errors: { _form: `Erro ao buscar analytics de uso dos planos: ${error.message}` },
      };
    }

    return {
      success: true,
      data: analyticsData,
    };
  } catch (error) {
    console.error('Erro ao buscar analytics de uso dos planos:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
} 