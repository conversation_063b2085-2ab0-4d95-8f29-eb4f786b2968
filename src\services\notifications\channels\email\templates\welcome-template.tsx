/**
 * Template de e-mail de boas-vindas para novos alunos
 * Tom caloroso e motivacional
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface WelcomeTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  tenantSlug?: string;

  // Dados do estudante e matrícula
  studentName: string;
  planName: string;
  startDate: string;
  enrollmentDate: string;

  // Informações opcionais
  instructorName?: string;
  firstClassDate?: string;
  academyAddress?: string;
  academyPhone?: string;
  welcomeMessage?: string;
}

export function WelcomeTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  tenantSlug,
  studentName,
  planName,
  startDate,
  enrollmentDate,
  instructorName,
  firstClassDate,
  academyAddress,
  academyPhone,
  welcomeMessage
}: WelcomeTemplateProps) {
  const formatDate = (dateString: string) => {
    if (!dateString || dateString === 'undefined' || dateString === 'null') {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(new Date());
    }

    // Se a data está no formato YYYY-MM-DD (apenas data), tratar como data local
    // para evitar problemas de timezone
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const [year, month, day] = dateString.split('-').map(Number);
      const date = new Date(year, month - 1, day); // month é 0-indexed
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(date);
    }

    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(new Date());
    }

    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  const startDateFormatted = formatDate(startDate);
  const enrollmentDateFormatted = formatDate(enrollmentDate);
  const firstClassDateFormatted = firstClassDate ? formatDate(firstClassDate) : null;

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`Bem-vindo(a) à ${academyName}! Sua jornada começa agora.`}
    >
      <EmailHeading level={1} color={primaryColor}>
        🎉 Bem-vindo(a) à Família {academyName}!
      </EmailHeading>

      <EmailText>
        Olá <strong>{studentName}</strong>,
      </EmailText>

      <EmailText>
        É com grande alegria que damos as boas-vindas à nossa academia! 
        Estamos muito animados para acompanhar sua jornada de evolução e crescimento.
      </EmailText>

      {welcomeMessage && (
        <div style={{
          backgroundColor: '#f0f9ff',
          padding: '20px',
          borderRadius: '8px',
          border: `1px solid ${primaryColor}20`,
          margin: '24px 0'
        }}>
          <EmailText color={primaryColor}>
            <em>"{welcomeMessage}"</em>
          </EmailText>
        </div>
      )}

      <EmailDivider color={primaryColor} />

      {/* Detalhes da matrícula */}
      <div style={{
        backgroundColor: '#f8fafc',
        padding: '24px',
        borderRadius: '8px',
        border: `1px solid ${primaryColor}20`
      }}>
        <EmailHeading level={3} color={primaryColor}>
          📋 Detalhes da sua Matrícula
        </EmailHeading>

        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color="#374151">
            <strong>Plano Contratado:</strong> {planName}
          </EmailText>
          <EmailText variant="small" color="#374151">
            <strong>Data da Matrícula:</strong> {enrollmentDateFormatted}
          </EmailText>
          <EmailText variant="small" color="#374151">
            <strong>Início das Atividades:</strong> {startDateFormatted}
          </EmailText>
          {instructorName && (
            <EmailText variant="small" color="#374151">
              <strong>Instrutor Principal:</strong> {instructorName}
            </EmailText>
          )}
          {firstClassDateFormatted && (
            <EmailText variant="small" color="#374151">
              <strong>Primeira Aula:</strong> {firstClassDateFormatted}
            </EmailText>
          )}
        </div>
      </div>

      <EmailDivider />

      {/* Próximos passos */}
      <EmailHeading level={2} color={primaryColor}>
        🚀 Próximos Passos
      </EmailHeading>

      <div style={{ marginBottom: '24px' }}>
        <EmailText>
          <strong>1. Prepare-se para seu primeiro treino</strong><br />
          Traga seu kimono confortavel. Chegue com alguns minutos de antecedência.
        </EmailText>

        <EmailText>
          <strong>2. Conheça nossa equipe</strong><br />
          Nossa equipe está sempre disponível para ajudar e esclarecer dúvidas.
        </EmailText>

        <EmailText>
          <strong>3. Mantenha-se motivado</strong><br />
          Lembre-se: cada treino é um passo em direção aos seus objetivos!
        </EmailText>
      </div>

      {/* Botão de acesso */}
      {(tenantSlug && process.env.NEXT_PUBLIC_BASE_DOMAIN) && (
        <div style={{ textAlign: 'center', margin: '32px 0' }}>
          <EmailButton
            href={`https://${tenantSlug}.${process.env.NEXT_PUBLIC_BASE_DOMAIN}/home`}
            primaryColor={primaryColor}
          >
            Acessar Minha Área
          </EmailButton>
        </div>
      )}

      {/* Informações de contato */}
      {(academyAddress || academyPhone) && (
        <>
          <EmailDivider />
          <EmailHeading level={3} color={primaryColor}>
            📍 Informações de Contato
          </EmailHeading>

          {academyAddress && (
            <EmailText variant="small" color="#64748b">
              <strong>Endereço:</strong> {academyAddress}
            </EmailText>
          )}
          {academyPhone && (
            <EmailText variant="small" color="#64748b">
              <strong>Telefone:</strong> {academyPhone}
            </EmailText>
          )}
        </>
      )}

      {/* Mensagem motivacional final */}
      <div style={{
        backgroundColor: '#f0fdf4',
        padding: '20px',
        borderRadius: '8px',
        border: '1px solid #bbf7d0',
        marginTop: '32px'
      }}>
        <EmailText color="#166534">
          💪 <strong>Sua jornada começa agora!</strong> Estamos aqui para apoiar 
          cada passo do seu desenvolvimento. Seja bem-vindo(a) à família {academyName}!
        </EmailText>
      </div>

      <EmailText variant="small" color="#64748b">
        Qualquer dúvida, não hesite em entrar em contato conosco. 
        Estamos ansiosos para vê-lo(a) em ação!
      </EmailText>
    </BaseEmailTemplate>
  );
}
