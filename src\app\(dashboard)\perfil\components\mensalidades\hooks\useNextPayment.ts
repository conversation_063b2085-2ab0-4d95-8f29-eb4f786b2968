'use client'

import { useEffect, useState, useCallback } from "react"
import { getNextPaymentDate } from "../../../actions/plan-actions"

interface NextPaymentData {
  payment_id: string
  due_date: string
  amount: number
  status: string
  payment_type: string
}

interface UseNextPaymentReturn {
  nextPayment: NextPaymentData | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useNextPayment(membershipId: string | null): UseNextPaymentReturn {
  const [nextPayment, setNextPayment] = useState<NextPaymentData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchNextPayment = useCallback(async () => {
    if (!membershipId) {
      setNextPayment(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const result = await getNextPaymentDate(membershipId)

      if (result.success) {
        setNextPayment(result.data || null)
      } else {
        setError(result.errors?._form || 'Erro ao buscar próxima data de cobrança')
        setNextPayment(null)
      }
    } catch (err) {
      console.error('Erro ao buscar próxima data de cobrança:', err)
      setError('Erro interno ao buscar próxima data de cobrança')
      setNextPayment(null)
    } finally {
      setLoading(false)
    }
  }, [membershipId])

  const refetch = useCallback(async () => {
    await fetchNextPayment()
  }, [fetchNextPayment])

  useEffect(() => {
    fetchNextPayment()
  }, [fetchNextPayment])

  return {
    nextPayment,
    loading,
    error,
    refetch
  }
}
