'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  EllipsisVerticalIcon,
  CheckIcon,
  ArchiveBoxIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import type { 
  Notification, 
  NotificationCategory, 
  NotificationPriority 
} from '@/services/notifications/types/notification-types';

interface NotificationItemProps {
  notification: Notification;
  selected?: boolean;
  onSelect?: (selected: boolean) => void;
  onMarkAsRead?: () => void;
  onArchive?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
}

// Função para obter ícone baseado na categoria
function getCategoryIcon(category: NotificationCategory) {
  switch (category) {
    case 'success':
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
    case 'error':
      return <XCircleIcon className="h-5 w-5 text-red-500" />;
    case 'alert':
      return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
    case 'info':
    case 'reminder':
    default:
      return <InformationCircleIcon className="h-5 w-5 text-blue-500" />;
  }
}

// Função para obter cor da prioridade
function getPriorityColor(priority: NotificationPriority) {
  switch (priority) {
    case 'urgent':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    case 'high':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
    case 'medium':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    case 'low':
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
}

// Função para formatar tempo relativo
function formatRelativeTime(dateString: string) {
  try {
    return formatDistanceToNow(new Date(dateString), {
      addSuffix: true
    });
  } catch {
    return 'Agora mesmo';
  }
}

export function NotificationItem({
  notification,
  selected = false,
  onSelect,
  onMarkAsRead,
  onArchive,
  onDelete,
  showActions = true
}: NotificationItemProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleAction = async (action: () => Promise<void> | void) => {
    setIsLoading(true);
    try {
      await action();
    } catch (error) {
      console.error('Erro ao executar ação:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const isUnread = notification.status === 'unread';
  const isArchived = notification.status === 'archived';

  return (
    <Card className={cn(
      'transition-all duration-200',
      isUnread && 'border-l-4 border-l-primary bg-primary/5',
      selected && 'ring-2 ring-primary'
    )}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Checkbox de seleção */}
          {showActions && onSelect && (
            <Checkbox
              checked={selected}
              onCheckedChange={onSelect}
              className="mt-1"
            />
          )}

          {/* Ícone da categoria */}
          <div className="flex-shrink-0 mt-1">
            {getCategoryIcon(notification.category)}
          </div>

          {/* Conteúdo principal */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <div className="flex-1">
                <h3 className={cn(
                  'text-sm font-medium',
                  isUnread ? 'text-gray-900 dark:text-gray-100' : 'text-gray-700 dark:text-gray-300'
                )}>
                  {notification.title}
                </h3>
                
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  {notification.message}
                </p>

                <div className="mt-2 flex items-center gap-2 flex-wrap">
                  {/* Badge de prioridade */}
                  <Badge 
                    variant="secondary" 
                    className={cn('text-xs', getPriorityColor(notification.priority))}
                  >
                    {notification.priority === 'urgent' && 'Urgente'}
                    {notification.priority === 'high' && 'Alta'}
                    {notification.priority === 'medium' && 'Média'}
                    {notification.priority === 'low' && 'Baixa'}
                  </Badge>

                  {/* Badge de tipo */}
                  <Badge variant="outline" className="text-xs">
                    {notification.type === 'payment' && 'Pagamento'}
                    {notification.type === 'class' && 'Aula'}
                    {notification.type === 'system' && 'Sistema'}
                    {notification.type === 'enrollment' && 'Matrícula'}
                    {notification.type === 'enrollment_payment_due' && 'Vencimento de Matrícula'}
                    {notification.type === 'expense_due' && 'Despesa'}
                    {notification.type === 'new_enrollment_notification' && 'Nova Matrícula'}
                    {notification.type === 'event' && 'Evento'}
                  </Badge>

                  {/* Badge de status */}
                  {isArchived && (
                    <Badge variant="secondary" className="text-xs">
                      Arquivada
                    </Badge>
                  )}

                  {/* Timestamp */}
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {formatRelativeTime(notification.created_at)}
                  </span>
                </div>
              </div>

              {/* Menu de ações */}
              {showActions && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      disabled={isLoading}
                    >
                      <EllipsisVerticalIcon className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {isUnread && onMarkAsRead && (
                      <DropdownMenuItem onClick={() => handleAction(onMarkAsRead)}>
                        <CheckIcon className="h-4 w-4 mr-2" />
                        Marcar como Lida
                      </DropdownMenuItem>
                    )}
                    
                    {!isArchived && onArchive && (
                      <DropdownMenuItem onClick={() => handleAction(onArchive)}>
                        <ArchiveBoxIcon className="h-4 w-4 mr-2" />
                        Arquivar
                      </DropdownMenuItem>
                    )}
                    
                    {onDelete && (
                      <DropdownMenuItem 
                        onClick={() => handleAction(onDelete)}
                        className="text-red-600 dark:text-red-400"
                      >
                        <TrashIcon className="h-4 w-4 mr-2" />
                        Excluir
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
