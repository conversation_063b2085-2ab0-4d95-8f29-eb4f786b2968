/**
 * Schemas Zod para validação do sistema de notificações
 * Baseado nos tipos TypeScript definidos
 */

import { z } from 'zod';

// Schemas para tipos básicos
export const NotificationTypeSchema = z.enum([
  'payment',
  'class',
  'system',
  'enrollment',
  'enrollment_payment_due',
  'expense_due',
  'new_enrollment_notification',
  'event'
]);

export const NotificationCategorySchema = z.enum([
  'reminder',
  'alert',
  'info',
  'success',
  'error'
]);

export const NotificationPrioritySchema = z.enum([
  'low',
  'medium',
  'high',
  'urgent'
]);

export const NotificationStatusSchema = z.enum([
  'unread',
  'read',
  'archived',
  'deleted'
]);

export const NotificationChannelSchema = z.enum([
  'in_app',
  'email',
  'whatsapp'
]);

// Schema para criar notificação
export const CreateNotificationSchema = z.object({
  user_id: z.string().uuid('ID do usuário deve ser um UUID válido'),
  type: NotificationTypeSchema,
  category: NotificationCategorySchema,
  priority: NotificationPrioritySchema.default('medium'),
  title: z.string().min(1, 'Título é obrigatório').max(255, 'Título deve ter no máximo 255 caracteres'),
  message: z.string().min(1, 'Mensagem é obrigatória'),
  data: z.record(z.any()).default({}),
  channels: z.array(NotificationChannelSchema).default(['in_app']),
  scheduled_for: z.string().datetime().optional(),
  expires_at: z.string().datetime().optional()
});

// Schema para atualizar notificação
export const UpdateNotificationSchema = z.object({
  status: NotificationStatusSchema.optional(),
  read_at: z.string().datetime().optional()
});

// Schema para filtros de notificação
export const NotificationFiltersSchema = z.object({
  status: z.array(NotificationStatusSchema).optional(),
  type: z.array(NotificationTypeSchema).optional(),
  category: z.array(NotificationCategorySchema).optional(),
  priority: z.array(NotificationPrioritySchema).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  search: z.string().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20)
});

// Schema personalizado para validação de hora que aceita HH:MM ou HH:MM:SS
const timeSchema = z.string()
  .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/, 'Formato de hora inválido (HH:MM ou HH:MM:SS)')
  .transform((value) => {
    // Se o valor tem segundos (HH:MM:SS), remove os segundos
    if (value.length === 8 && value.includes(':')) {
      return value.substring(0, 5); // Retorna apenas HH:MM
    }
    return value; // Já está no formato HH:MM
  })
  .optional();

// Schema para preferências de notificação
export const CreateNotificationPreferencesSchema = z.object({
  notification_type: NotificationTypeSchema,
  in_app_enabled: z.boolean().default(true),
  email_enabled: z.boolean().default(true),
  whatsapp_enabled: z.boolean().default(false),
  quiet_hours_start: timeSchema,
  quiet_hours_end: timeSchema,
  timezone: z.string().default('America/Sao_Paulo')
});

export const UpdateNotificationPreferencesSchema = z.object({
  in_app_enabled: z.boolean().optional(),
  email_enabled: z.boolean().optional(),
  whatsapp_enabled: z.boolean().optional(),
  quiet_hours_start: timeSchema,
  quiet_hours_end: timeSchema,
  timezone: z.string().optional()
});

// Schema para templates de notificação
export const CreateTemplateSchema = z.object({
  type: NotificationTypeSchema,
  channel: NotificationChannelSchema,
  name: z.string().min(1, 'Nome é obrigatório').max(100, 'Nome deve ter no máximo 100 caracteres'),
  subject_template: z.string().optional(),
  body_template: z.string().min(1, 'Template do corpo é obrigatório'),
  parent_template_id: z.string().uuid().optional()
});

export const UpdateTemplateSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório').max(100, 'Nome deve ter no máximo 100 caracteres').optional(),
  subject_template: z.string().optional(),
  body_template: z.string().min(1, 'Template do corpo é obrigatório').optional(),
  is_active: z.boolean().optional()
});

// Schema para validação de variáveis de template
export const TemplateVariableSchema = z.object({
  template_type: NotificationTypeSchema,
  variable_name: z.string().min(1, 'Nome da variável é obrigatório'),
  variable_key: z.string().min(1, 'Chave da variável é obrigatória'),
  description: z.string().optional(),
  data_type: z.enum(['string', 'number', 'date', 'boolean', 'url']),
  is_required: z.boolean().default(false),
  default_value: z.string().optional(),
  example_value: z.string().optional(),
  category: z.string().optional()
});

// Schema para preview de template
export const TemplatePreviewSchema = z.object({
  template_id: z.string().uuid('ID do template deve ser um UUID válido'),
  variables: z.record(z.any())
});

// Schema para marcar notificação como lida
export const MarkAsReadSchema = z.object({
  notification_id: z.string().uuid('ID da notificação deve ser um UUID válido')
});

// Schema para marcar todas como lidas
export const MarkAllAsReadSchema = z.object({
  user_id: z.string().uuid('ID do usuário deve ser um UUID válido')
});

// Schema para arquivar notificação
export const ArchiveNotificationSchema = z.object({
  notification_id: z.string().uuid('ID da notificação deve ser um UUID válido')
});

// Schema para deletar notificação
export const DeleteNotificationSchema = z.object({
  notification_id: z.string().uuid('ID da notificação deve ser um UUID válido')
});

// Schema para buscar notificações
export const GetNotificationsSchema = z.object({
  user_id: z.string().uuid('ID do usuário deve ser um UUID válido'),
  filters: NotificationFiltersSchema.optional()
});

// Schema para contar notificações não lidas
export const GetUnreadCountSchema = z.object({
  user_id: z.string().uuid('ID do usuário deve ser um UUID válido')
});

// Tipos inferidos dos schemas
export type CreateNotificationInput = z.infer<typeof CreateNotificationSchema>;
export type UpdateNotificationInput = z.infer<typeof UpdateNotificationSchema>;
export type NotificationFiltersInput = z.infer<typeof NotificationFiltersSchema>;
export type CreateNotificationPreferencesInput = z.infer<typeof CreateNotificationPreferencesSchema>;
export type UpdateNotificationPreferencesInput = z.infer<typeof UpdateNotificationPreferencesSchema>;
export type CreateTemplateInput = z.infer<typeof CreateTemplateSchema>;
export type UpdateTemplateInput = z.infer<typeof UpdateTemplateSchema>;
export type TemplatePreviewInput = z.infer<typeof TemplatePreviewSchema>;
