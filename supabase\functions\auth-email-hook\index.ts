/**
 * Edge Function para customizar emails de autenticação do Supabase
 * Usa React Email para gerar templates personalizados por academia
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { render } from 'https://esm.sh/@react-email/render@1.1.3'

// Importar o template React Email local
import { PasswordResetTemplate } from './password-reset-template.tsx'

interface AuthEmailHookPayload {
  event: string
  user_id: string
  email: string
  email_action_type: 'signup' | 'recovery' | 'invite' | 'magic_link' | 'email_change'
  token_hash: string
  token: string
  redirect_to?: string
  site_url: string
}

interface AcademyConfig {
  id: string
  name: string
  logo?: string
  primary_color?: string
  secondary_color?: string
  support_email?: string
  support_phone?: string
}

serve(async (req) => {
  try {
    // Verificar se é um POST request
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 })
    }

    // Parse do payload
    const payload: AuthEmailHookPayload = await req.json()
    
    // Só processar emails de recovery (reset de senha)
    if (payload.email_action_type !== 'recovery') {
      return new Response(JSON.stringify({ message: 'Email type not handled' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 200
      })
    }

    // Criar cliente Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Buscar dados do usuário e academia
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        name,
        tenant_id,
        tenants (
          id,
          name,
          logo_url,
          primary_color,
          secondary_color,
          support_email,
          support_phone
        )
      `)
      .eq('id', payload.user_id)
      .single()

    if (userError || !userData) {
      console.error('Erro ao buscar dados do usuário:', userError)
      return new Response(JSON.stringify({ error: 'User not found' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 404
      })
    }

    // Configurar dados da academia
    const academy: AcademyConfig = {
      id: userData.tenants?.id || 'default',
      name: userData.tenants?.name || 'Academia',
      logo: userData.tenants?.logo_url,
      primary_color: userData.tenants?.primary_color || '#007291',
      secondary_color: userData.tenants?.secondary_color || '#004E89',
      support_email: userData.tenants?.support_email,
      support_phone: userData.tenants?.support_phone
    }

    // Construir URL de reset
    const resetUrl = `${payload.site_url}/auth/confirm?token_hash=${payload.token_hash}&type=recovery&next=/home/<USER>

    // Gerar HTML usando React Email
    const emailHtml = render(
      PasswordResetTemplate({
        academyName: academy.name,
        academyLogo: academy.logo,
        primaryColor: academy.primary_color,
        secondaryColor: academy.secondary_color,
        userEmail: payload.email,
        userName: userData.name,
        resetUrl: resetUrl,
        tokenHash: payload.token_hash,
        siteUrl: payload.site_url,
        expirationTime: '1 hora',
        supportEmail: academy.support_email,
        supportPhone: academy.support_phone
      })
    )

    // Enviar email usando um provedor de email (exemplo com Resend)
    const resendApiKey = Deno.env.get('RESEND_API_KEY')
    
    if (resendApiKey) {
      const emailResponse = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${resendApiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          from: academy.support_email || `noreply@${academy.name.toLowerCase().replace(/\s+/g, '')}.com`,
          to: payload.email,
          subject: `Redefinir senha - ${academy.name}`,
          html: emailHtml
        })
      })

      if (!emailResponse.ok) {
        const errorData = await emailResponse.text()
        console.error('Erro ao enviar email:', errorData)
        throw new Error('Failed to send email')
      }

      console.log('Email de reset enviado com sucesso para:', payload.email)
    }

    return new Response(
      JSON.stringify({ 
        message: 'Email sent successfully',
        academy: academy.name,
        user: payload.email
      }),
      {
        headers: { 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Erro na Edge Function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message
      }),
      {
        headers: { 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
