/**
 * Canal de e-mail para o sistema de notificações
 * Utiliza provedores de e-mail para envio de notificações
 */

import { NotificationChannelBase, NotificationChannelData, NotificationChannelResult, BatchNotificationResult, DeliveryStatus } from '../base/notification-channel';
import { EmailProvider, EmailData } from './providers/email-provider';
import { ReactEmailEngine } from './react-email-engine';
import { NotificationConfigService } from './notification-config-service';
import { NotificationLogger, NotificationError, NotificationErrorCodes } from '../../core/notification-logger';
import { createAdminClient } from '@/services/supabase/server';

export class EmailChannel extends NotificationChannelBase {
  private provider: EmailProvider;
  private templateEngine: ReactEmailEngine;
  private configService: NotificationConfigService;
  private logger: NotificationLogger;

  constructor(provider: EmailProvider, templateEngine: ReactEmailEngine, configService: NotificationConfigService) {
    super('email');
    this.provider = provider;
    this.templateEngine = templateEngine;
    this.configService = configService;
    this.logger = NotificationLogger.forComponent('EmailChannel');
  }

  /**
   * Envia uma notificação por e-mail
   * Assume que permissões já foram verificadas pelo dispatcher
   */
  async send(data: NotificationChannelData): Promise<NotificationChannelResult> {
    const context = NotificationLogger.createNotificationContext({
      tenantId: data.tenantId,
      userId: data.userId,
      notificationType: data.type,
      channel: 'email',
      templateId: data.templateId
    });

    this.logger.startOperation('send email', context);
    const startTime = Date.now();

    try {
      // Buscar configurações do tenant
      const tenantConfig = await this.configService.getTenantConfig(data.tenantId);

      if (!tenantConfig.emailEnabled) {
        const error = new NotificationError(
          'E-mail desabilitado para este tenant',
          NotificationErrorCodes.CHANNEL_DISABLED,
          context
        );
        this.logger.failOperation('send email', error, context);
        return {
          success: false,
          error: error.message
        };
      }

      // Preparar dados do e-mail usando método auxiliar
      const emailData = await this.prepareEmailData(data, tenantConfig);
      if (!emailData) {
        const error = new NotificationError(
          'E-mail do usuário não encontrado',
          NotificationErrorCodes.USER_EMAIL_NOT_FOUND,
          context
        );
        this.logger.failOperation('send email', error, context);
        return {
          success: false,
          error: error.message
        };
      }

      const result = await this.provider.send(emailData);
      const duration = Date.now() - startTime;

      if (result.success) {
        this.logger.endOperation('send email', { ...context, messageId: result.messageId }, duration);
      } else {
        this.logger.error('Email send failed', { ...context, providerError: result.error });
      }

      return {
        success: result.success,
        messageId: result.messageId,
        error: result.error,
        metadata: {
          provider: this.provider.getProviderName(),
          ...result.metadata
        }
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const notificationError = new NotificationError(
        'Erro interno ao enviar e-mail',
        NotificationErrorCodes.INTERNAL_ERROR,
        context,
        error instanceof Error ? error : undefined
      );

      this.logger.failOperation('send email', notificationError, context);
      this.logger.performance('send email (failed)', duration, context);

      return {
        success: false,
        error: notificationError.message
      };
    }
  }

  /**
   * Envia múltiplas notificações por e-mail em lote
   */
  async sendBatch(notifications: NotificationChannelData[]): Promise<BatchNotificationResult> {
    const results: NotificationChannelResult[] = [];
    let totalSent = 0;
    let totalFailed = 0;
    const errors: string[] = [];

    // Inicializar array de resultados com o tamanho correto
    for (let i = 0; i < notifications.length; i++) {
      results.push({
        success: false,
        error: 'Processando...'
      });
    }

    // Agrupar por tenant para otimizar configurações, mantendo índices originais
    const notificationsByTenant = new Map<string, { notifications: NotificationChannelData[], indices: number[] }>();

    for (let i = 0; i < notifications.length; i++) {
      const notification = notifications[i];
      const tenantData = notificationsByTenant.get(notification.tenantId) || { notifications: [], indices: [] };
      tenantData.notifications.push(notification);
      tenantData.indices.push(i);
      notificationsByTenant.set(notification.tenantId, tenantData);
    }

    // Processar cada tenant
    for (const [tenantId, { notifications: tenantNotifications, indices: tenantIndices }] of notificationsByTenant) {
      try {
        const tenantConfig = await this.configService.getTenantConfig(tenantId);

        if (!tenantConfig.emailEnabled) {
          // E-mail desabilitado para este tenant
          for (let i = 0; i < tenantIndices.length; i++) {
            const originalIndex = tenantIndices[i];
            results[originalIndex] = {
              success: false,
              error: 'E-mail desabilitado para este tenant'
            };
            totalFailed++;
          }
          errors.push('E-mail desabilitado para este tenant');
          continue;
        }

        // Preparar e-mails para este tenant, mantendo mapeamento de índices
        const emailsToSend: EmailData[] = [];
        const emailToOriginalIndexMap: number[] = []; // Mapeia índice do email para índice original

        for (let i = 0; i < tenantNotifications.length; i++) {
          const notification = tenantNotifications[i];
          const originalIndex = tenantIndices[i];

          try {
            // Usar método auxiliar para preparar dados do e-mail
            const emailData = await this.prepareEmailData(notification, tenantConfig);
            if (!emailData) {
              results[originalIndex] = {
                success: false,
                error: 'E-mail do usuário não encontrado'
              };
              totalFailed++;
              errors.push('E-mail do usuário não encontrado');
              continue;
            }

            emailsToSend.push(emailData);
            emailToOriginalIndexMap.push(originalIndex);

          } catch (error) {
            results[originalIndex] = {
              success: false,
              error: error instanceof Error ? error.message : 'Erro ao preparar e-mail'
            };
            totalFailed++;
            errors.push(error instanceof Error ? error.message : 'Erro ao preparar e-mail');
          }
        }

        // Enviar e-mails em lote para este tenant
        if (emailsToSend.length > 0) {
          this.logger.debug('Sending batch emails', {
            tenantId,
            emailCount: emailsToSend.length,
            originalIndicesCount: emailToOriginalIndexMap.length
          });

          const batchResult = await this.provider.sendBatch(emailsToSend);

          this.logger.debug('Batch result received', {
            tenantId,
            resultsCount: batchResult.results.length,
            expectedCount: emailToOriginalIndexMap.length,
            totalSent: batchResult.totalSent,
            totalFailed: batchResult.totalFailed
          });

          // Mapear resultados de volta para os índices originais
          for (let i = 0; i < batchResult.results.length; i++) {
            const originalIndex = emailToOriginalIndexMap[i];
            if (originalIndex !== undefined) {
              results[originalIndex] = batchResult.results[i];
              if (batchResult.results[i].success) {
                totalSent++;
              } else {
                totalFailed++;
              }
            }
          }

          // Se houver menos resultados do que esperado, marcar os restantes como falha
          if (batchResult.results.length < emailToOriginalIndexMap.length) {
            this.logger.warn('Missing results from provider', {
              tenantId,
              expected: emailToOriginalIndexMap.length,
              received: batchResult.results.length,
              missing: emailToOriginalIndexMap.length - batchResult.results.length
            });

            for (let i = batchResult.results.length; i < emailToOriginalIndexMap.length; i++) {
              const originalIndex = emailToOriginalIndexMap[i];
              if (originalIndex !== undefined) {
                results[originalIndex] = {
                  success: false,
                  error: 'Resultado não retornado pelo provedor'
                };
                totalFailed++;
              }
            }
          }

          errors.push(...batchResult.errors);
        }

      } catch (error) {
        // Erro ao processar tenant
        for (const originalIndex of tenantIndices) {
          results[originalIndex] = {
            success: false,
            error: error instanceof Error ? error.message : 'Erro ao processar tenant'
          };
          totalFailed++;
        }
        errors.push(error instanceof Error ? error.message : 'Erro ao processar tenant');
      }
    }

    return {
      success: totalFailed === 0,
      results,
      totalSent,
      totalFailed,
      errors
    };
  }

  /**
   * Verifica o status de entrega de um e-mail
   */
  async getDeliveryStatus(messageId: string): Promise<DeliveryStatus> {
    try {
      const status = await this.provider.getDeliveryStatus(messageId);
      
      return {
        messageId: status.messageId,
        status: status.status,
        timestamp: status.timestamp,
        error: status.error,
        metadata: {
          provider: this.provider.getProviderName(),
          recipient: status.recipient,
          events: status.events
        }
      };

    } catch (error) {
      console.error('Erro ao verificar status de entrega:', error);
      return {
        messageId,
        status: 'failed',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Valida se o canal está configurado corretamente
   */
  async validateConfiguration(): Promise<boolean> {
    try {
      return await this.provider.validateConfiguration();
    } catch (error) {
      console.error('Erro na validação do canal de e-mail:', error);
      return false;
    }
  }

  /**
   * Prepara os dados do e-mail para envio
   */
  private async prepareEmailData(
    data: NotificationChannelData,
    tenantConfig: any
  ): Promise<EmailData | null> {
    const context = NotificationLogger.createNotificationContext({
      tenantId: data.tenantId,
      userId: data.userId,
      notificationType: data.type,
      templateId: data.templateId
    });

    try {
      // Usar recipientEmail se fornecido, senão buscar e-mail do usuário
      let userEmail: string | null = null;

      if (data.recipientEmail) {
        userEmail = data.recipientEmail;
        this.logger.debug('Using provided recipient email', { ...context, recipientEmail: data.recipientEmail });
      } else {
        userEmail = await this.getUserEmail(data.userId);
        this.logger.debug('Using user email from database', { ...context, userEmail });
      }

      if (!userEmail) {
        this.logger.warn('User email not found', context);
        return null;
      }

      // Renderizar template de e-mail
      const templateId = data.templateId || `${data.type}_default`;
      this.logger.debug('Rendering email template', { ...context, templateId });

      const renderedEmail = await this.templateEngine.renderEmail(
        templateId,
        {
          ...data.variables,
          academyName: tenantConfig.tenantName,
          academyLogo: tenantConfig.academyLogo,
          primaryColor: tenantConfig.primaryColor,
          secondaryColor: tenantConfig.secondaryColor,
          tenantSlug: tenantConfig.tenantSlug
        }
      );

      // Preparar dados do e-mail
      return {
        to: userEmail,
        from: tenantConfig.emailFromDomain,
        fromName: tenantConfig.emailFromName,
        subject: renderedEmail.subject,
        html: renderedEmail.html,
        text: renderedEmail.text,
        metadata: {
          tenantId: data.tenantId,
          userId: data.userId,
          notificationType: data.type,
          templateId: data.templateId
        }
      };
    } catch (error) {
      const notificationError = new NotificationError(
        'Erro ao preparar dados do e-mail',
        NotificationErrorCodes.TEMPLATE_RENDER_ERROR,
        context,
        error instanceof Error ? error : undefined
      );
      this.logger.error('Failed to prepare email data', context, notificationError);
      return null;
    }
  }

  /**
   * Obtém o e-mail do usuário
   */
  private async getUserEmail(userId: string): Promise<string | null> {
    try {
      const supabase = await createAdminClient();

      // Buscar e-mail do usuário na tabela users
      const { data: user, error } = await supabase
        .from('users')
        .select('email')
        .eq('id', userId)
        .single();

      if (error) {
        this.logger.error('Database error fetching user', { userId },
          new NotificationError('Erro ao buscar usuário', NotificationErrorCodes.DATABASE_ERROR, { userId }, error)
        );
        return null;
      }

      return user?.email || null;
    } catch (error) {
      this.logger.error('Failed to get user email', { userId },
        new NotificationError('Erro ao buscar e-mail do usuário', NotificationErrorCodes.DATABASE_ERROR, { userId }, error instanceof Error ? error : undefined)
      );
      return null;
    }
  }
}
