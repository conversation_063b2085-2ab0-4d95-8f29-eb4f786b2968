/**
 * Template de e-mail para vencimento de pagamento de matrícula
 * Específico para pagamentos de matrícula de alunos
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface EnrollmentPaymentDueTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  tenantSlug?: string;
  
  // Dados do estudante e matrícula
  studentName: string;
  amount: number;
  dueDate: string;
  enrollmentDate: string;
  planName: string;
  paymentMethod?: string;
  invoiceUrl?: string;
  paymentId?: string;
  
  // Configurações opcionais
  currency?: string;
  lateFee?: number;
  gracePeriod?: number;
  installmentNumber?: number;
  totalInstallments?: number;
}

export function EnrollmentPaymentDueTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  tenantSlug,
  studentName,
  amount,
  dueDate,
  enrollmentDate,
  planName,
  paymentMethod,
  invoiceUrl,
  paymentId,
  currency = 'BRL',
  lateFee,
  gracePeriod,
  installmentNumber,
  totalInstallments
}: EnrollmentPaymentDueTemplateProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    // Se a data está no formato YYYY-MM-DD (apenas data), tratar como data local
    // para evitar problemas de timezone
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const [year, month, day] = dateString.split('-').map(Number);
      const date = new Date(year, month - 1, day); // month é 0-indexed
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(date);
    }

    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(new Date(dateString));
  };

  const isOverdue = new Date(dueDate) < new Date();
  const dueDateFormatted = formatDate(dueDate);
  const enrollmentDateFormatted = formatDate(enrollmentDate);

  const getInstallmentText = () => {
    if (installmentNumber && totalInstallments) {
      return `${installmentNumber}ª de ${totalInstallments} parcelas`;
    }
    return 'Pagamento único';
  };

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`Vencimento de matrícula - ${planName} - ${formatCurrency(amount)}`}
    >
      <EmailHeading level={1} color={primaryColor}>
        {isOverdue ? '⚠️ Pagamento de Matrícula em Atraso' : '📋 Vencimento de Pagamento de Matrícula'}
      </EmailHeading>

      <EmailText>
        Olá <strong>{studentName}</strong>,
      </EmailText>

      <EmailText>
        {isOverdue ? (
          <>
            Identificamos que o pagamento da sua matrícula está em atraso. 
            Para manter sua vaga ativa e continuar participando das aulas, 
            é necessário regularizar esta situação o quanto antes.
          </>
        ) : (
          <>
            Este é um lembrete sobre o vencimento do pagamento da sua matrícula. 
            Mantenha sua situação em dia para garantir sua participação nas aulas!
          </>
        )}
      </EmailText>

      <EmailDivider color={primaryColor} />

      {/* Detalhes da matrícula */}
      <div style={{
        backgroundColor: '#f8fafc',
        padding: '24px',
        borderRadius: '8px',
        border: `1px solid ${primaryColor}20`
      }}>
        <EmailHeading level={3} color={primaryColor}>
          Detalhes da Matrícula
        </EmailHeading>

        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color="#64748b">
            <strong>Plano:</strong> {planName}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Data da Matrícula:</strong> {enrollmentDateFormatted}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Parcela:</strong> {getInstallmentText()}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Valor:</strong> {formatCurrency(amount)}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Vencimento:</strong> {dueDateFormatted}
          </EmailText>
          {paymentMethod && (
            <EmailText variant="small" color="#64748b">
              <strong>Forma de Pagamento:</strong> {paymentMethod}
            </EmailText>
          )}
        </div>

        {isOverdue && lateFee && (
          <div style={{
            backgroundColor: '#fef2f2',
            padding: '16px',
            borderRadius: '6px',
            border: '1px solid #fecaca',
            marginBottom: '16px'
          }}>
            <EmailText variant="small" color="#dc2626">
              <strong>⚠️ Multa por Atraso:</strong> {formatCurrency(lateFee)}
            </EmailText>
            <EmailText variant="small" color="#dc2626">
              <strong>Total a Pagar:</strong> {formatCurrency(amount + lateFee)}
            </EmailText>
          </div>
        )}

        {!isOverdue && gracePeriod && (
          <EmailText variant="small" color="#059669">
            💡 <strong>Dica:</strong> Você tem {gracePeriod} dias de tolerância após o vencimento.
          </EmailText>
        )}
      </div>

      <EmailDivider />

      {/* Botão de ação */}
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        {paymentId && tenantSlug ? (
          <EmailButton href={`https://${tenantSlug}.${process.env.NEXT_PUBLIC_BASE_DOMAIN}/checkout/${paymentId}`} primaryColor={primaryColor}>
            Pagar Matrícula
          </EmailButton>
        ) : invoiceUrl ? (
          <EmailButton href={invoiceUrl} primaryColor={primaryColor}>
            Ver Fatura da Matrícula
          </EmailButton>
        ) : (
          <EmailButton href="#" primaryColor={primaryColor}>
            Pagar Matrícula
          </EmailButton>
        )}
      </div>

      {/* Informações importantes sobre matrícula */}
      <div style={{
        backgroundColor: '#eff6ff',
        padding: '16px',
        borderRadius: '6px',
        border: '1px solid #bfdbfe',
        marginBottom: '24px'
      }}>
        <EmailText variant="small" color="#1d4ed8">
          <strong>📌 Importante sobre sua matrícula:</strong> O pagamento em dia 
          garante sua vaga nas turmas e acesso a todas as atividades da academia. 
          Em caso de atraso prolongado, sua matrícula pode ser suspensa.
        </EmailText>
      </div>

      {/* Informações de contato */}
      <EmailText variant="small" color="#64748b">
        <strong>Precisa de ajuda?</strong> Entre em contato conosco através dos nossos 
        canais de atendimento. Nossa equipe está pronta para esclarecer dúvidas 
        sobre sua matrícula e formas de pagamento.
      </EmailText>

      {isOverdue && (
        <div style={{
          backgroundColor: '#fffbeb',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #fed7aa',
          marginTop: '24px'
        }}>
          <EmailText variant="small" color="#d97706">
            <strong>⏰ Ação Necessária:</strong> Para evitar a suspensão da sua matrícula 
            e perda da vaga, regularize seu pagamento imediatamente. Em caso de 
            dificuldades financeiras, entre em contato para negociarmos uma solução.
          </EmailText>
        </div>
      )}
    </BaseEmailTemplate>
  );
}
