/**
 * Componente de preview de email em tempo real
 * Simula a aparência real de um email com dados da academia
 */

'use client';

import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { useTemplateRenderWithAcademy, useAcademyData } from '@/hooks/notifications/use-template-preview';

interface EmailPreviewProps {
  subject: string;
  body: string;
  variables: Record<string, any>;
  className?: string;
}

export function EmailPreview({ subject, body, variables, className }: EmailPreviewProps) {
  const { rendered: renderedSubject } = useTemplateRenderWithAcademy(subject, variables, 200);
  const { rendered: renderedBody } = useTemplateRenderWithAcademy(body, variables, 200);
  const { academyData, loading } = useAcademyData();

  if (loading) {
    return <EmailPreviewSkeleton />;
  }

  return (
    <div className={`flex-1 flex flex-col ${className || ''}`}>
      {/* Container do email */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border overflow-hidden flex-1 flex flex-col">
        {/* Barra superior sutil simulando cliente de email */}
        <div className="px-4 py-2 bg-gray-100 dark:bg-gray-700 border-b flex items-center gap-2 flex-shrink-0">
          <div className="flex gap-1">
            <div className="w-3 h-3 rounded-full bg-red-400"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
            <div className="w-3 h-3 rounded-full bg-green-400"></div>
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 ml-2">
            {renderedSubject || 'Sem assunto'}
          </div>
        </div>

        {/* Conteúdo do email */}
        <div className="flex-1 overflow-y-auto">
          <div
            className="w-full h-full text-gray-900"
            style={{
              '--tw-prose-links': academyData?.primary_color || '#3b82f6',
              '--tw-prose-headings': academyData?.secondary_color || '#1f2937',
              colorScheme: 'light'
            } as React.CSSProperties}
            dangerouslySetInnerHTML={{
              __html: renderedBody || '<div class="text-gray-500 italic text-center">Digite o conteúdo do template...</div>'
            }}
          />
        </div>
      </div>
    </div>
  );
}

function EmailPreviewSkeleton() {
  return (
    <div className="flex-1 flex flex-col">
      {/* Container do email skeleton */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border overflow-hidden max-w-4xl mx-auto flex-1 flex flex-col">
        {/* Barra superior skeleton */}
        <div className="px-4 py-2 bg-gray-100 dark:bg-gray-700 border-b flex items-center gap-2 flex-shrink-0">
          <div className="flex gap-1">
            <div className="w-3 h-3 rounded-full bg-gray-300"></div>
            <div className="w-3 h-3 rounded-full bg-gray-300"></div>
            <div className="w-3 h-3 rounded-full bg-gray-300"></div>
          </div>
          <Skeleton className="h-3 w-32 ml-2" />
        </div>

        {/* Conteúdo skeleton */}
        <div className="p-6 space-y-3 flex-1">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-4/5" />
        </div>
      </div>
    </div>
  );
}
