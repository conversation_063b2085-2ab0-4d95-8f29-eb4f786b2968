/**
 * Hook para gerenciar preferências de notificação do usuário
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/services/supabase/client';
import type {
  NotificationPreferences,
  NotificationType,
  CreateNotificationPreferencesData,
  UpdateNotificationPreferencesData,
} from '@/services/notifications/types/notification-types';
import {
  CreateNotificationPreferencesSchema,
  UpdateNotificationPreferencesSchema
} from '@/services/notifications/types/notification-schemas';

interface UseNotificationPreferencesOptions {
  userId: string;
  tenantId?: string;
}

interface UseNotificationPreferencesReturn {
  preferences: NotificationPreferences[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  updatePreferences: (id: string, data: UpdateNotificationPreferencesData) => Promise<void>;
  createPreferences: (data: CreateNotificationPreferencesData) => Promise<void>;
}

// Função utilitária para transformar horários do formato HH:MM:SS para HH:MM
function transformTimeFormat(time: string | null): string | null {
  if (!time) return null;
  return time.length === 8 ? time.substring(0, 5) : time;
}

// Função para transformar dados de preferências
function transformPreferencesData(preferences: any): any {
  return {
    ...preferences,
    quiet_hours_start: transformTimeFormat(preferences.quiet_hours_start),
    quiet_hours_end: transformTimeFormat(preferences.quiet_hours_end)
  };
}

export function useNotificationPreferences({
  userId,
  tenantId
}: UseNotificationPreferencesOptions): UseNotificationPreferencesReturn {
  const [preferences, setPreferences] = useState<NotificationPreferences[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Função para carregar preferências
  const loadPreferences = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const supabase = createClient();

      // Obter tenant_id do usuário se não fornecido
      let currentTenantId = tenantId;
      if (!currentTenantId) {
        const { data: userData } = await supabase
          .from('users')
          .select('tenant_id')
          .eq('id', userId)
          .single();
        currentTenantId = userData?.tenant_id;
      }

      const { data: preferencesData, error: fetchError } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', userId)
        .eq('tenant_id', currentTenantId);

      if (fetchError) {
        setError(`Erro ao carregar preferências: ${fetchError.message}`);
        return;
      }

      // Transformar os dados para garantir que os horários estejam no formato HH:MM
      const transformedData = (preferencesData || []).map(transformPreferencesData);

      setPreferences(transformedData);

    } catch (err) {
      if (err instanceof Error) {
        // Se for um erro do Zod, formatar melhor a mensagem
        if (err.name === 'ZodError') {
          const zodError = err as any;
          setError(`Erro de validação: ${JSON.stringify(zodError.issues)}`);
        } else {
          setError(err.message);
        }
      } else {
        setError('Erro desconhecido');
      }
    } finally {
      setLoading(false);
    }
  }, [userId, tenantId]);

  // Função para criar preferências
  const createPreferences = useCallback(async (data: CreateNotificationPreferencesData) => {
    try {
      setError(null);

      // Validar dados
      const validatedData = CreateNotificationPreferencesSchema.parse(data);

      const supabase = createClient();

      // Obter tenant_id do usuário se não fornecido
      let currentTenantId = tenantId;
      if (!currentTenantId) {
        const { data: userData } = await supabase
          .from('users')
          .select('tenant_id')
          .eq('id', userId)
          .single();
        currentTenantId = userData?.tenant_id;
      }

      const { data: newPreferences, error: createError } = await supabase
        .from('notification_preferences')
        .insert({
          tenant_id: currentTenantId,
          user_id: userId,
          ...validatedData
        })
        .select()
        .single();

      if (createError) {
        setError(`Erro ao criar preferências: ${createError.message}`);
        return;
      }

      // Transformar os dados antes de atualizar o estado
      const transformedPreferences = transformPreferencesData(newPreferences);

      // Atualizar estado local
      setPreferences(prev => [...prev, transformedPreferences]);

    } catch (err) {
      if (err instanceof Error) {
        // Se for um erro do Zod, formatar melhor a mensagem
        if (err.name === 'ZodError') {
          const zodError = err as any;
          setError(`Erro de validação: ${JSON.stringify(zodError.issues)}`);
        } else {
          setError(err.message);
        }
      } else {
        setError('Erro desconhecido');
      }
    }
  }, [userId, tenantId]);

  // Função para atualizar preferências
  const updatePreferences = useCallback(async (id: string, data: UpdateNotificationPreferencesData) => {
    try {
      setError(null);

      // Validar dados
      const validatedData = UpdateNotificationPreferencesSchema.parse(data);

      const supabase = createClient();

      const { data: updatedPreferences, error: updateError } = await supabase
        .from('notification_preferences')
        .update({
          ...validatedData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (updateError) {
        setError(`Erro ao atualizar preferências: ${updateError.message}`);
        return;
      }

      // Transformar os dados antes de atualizar o estado
      const transformedPreferences = transformPreferencesData(updatedPreferences);

      // Atualizar estado local
      setPreferences(prev =>
        prev.map(pref => pref.id === id ? transformedPreferences : pref)
      );

    } catch (err) {
      if (err instanceof Error) {
        // Se for um erro do Zod, formatar melhor a mensagem
        if (err.name === 'ZodError') {
          const zodError = err as any;
          setError(`Erro de validação: ${JSON.stringify(zodError.issues)}`);
        } else {
          setError(err.message);
        }
      } else {
        setError('Erro desconhecido');
      }
    }
  }, []);

  // Função para refresh manual
  const refresh = useCallback(async () => {
    await loadPreferences();
  }, [loadPreferences]);

  // Carregar preferências iniciais
  useEffect(() => {
    if (userId) {
      loadPreferences();
    }
  }, [userId, loadPreferences]);

  return {
    preferences,
    loading,
    error,
    refresh,
    updatePreferences,
    createPreferences
  };
}
