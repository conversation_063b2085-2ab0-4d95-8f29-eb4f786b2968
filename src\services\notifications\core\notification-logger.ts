/**
 * Logger estruturado para o sistema de notificações
 * Padroniza o logging em todos os componentes
 */

export interface LogContext {
  tenantId?: string;
  userId?: string;
  notificationType?: string;
  channel?: string;
  messageId?: string;
  templateId?: string;
  [key: string]: any;
}

export interface LogEntry {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  context?: LogContext;
  error?: Error;
  timestamp: Date;
  component: string;
}

export class NotificationLogger {
  private static instance: NotificationLogger | null = null;
  private component: string;

  private constructor(component: string) {
    this.component = component;
  }

  /**
   * Cria uma instância do logger para um componente específico
   */
  static forComponent(component: string): NotificationLogger {
    return new NotificationLogger(component);
  }

  /**
   * Log de debug
   */
  debug(message: string, context?: LogContext): void {
    this.log('debug', message, context);
  }

  /**
   * Log de informação
   */
  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
  }

  /**
   * Log de warning
   */
  warn(message: string, context?: LogContext, error?: Error): void {
    this.log('warn', message, context, error);
  }

  /**
   * Log de erro
   */
  error(message: string, context?: LogContext, error?: Error): void {
    this.log('error', message, context, error);
  }

  /**
   * Log estruturado
   */
  private log(level: LogEntry['level'], message: string, context?: LogContext, error?: Error): void {
    const entry: LogEntry = {
      level,
      message,
      context,
      error,
      timestamp: new Date(),
      component: this.component
    };

    // Formatar saída baseada no ambiente
    if (process.env.NODE_ENV === 'development') {
      this.logToConsole(entry);
    } else {
      this.logStructured(entry);
    }
  }

  /**
   * Log formatado para desenvolvimento
   */
  private logToConsole(entry: LogEntry): void {
    const emoji = this.getLevelEmoji(entry.level);
    const timestamp = entry.timestamp.toISOString();
    const contextStr = entry.context ? ` | ${JSON.stringify(entry.context)}` : '';
    
    const logMessage = `${emoji} [${timestamp}] [${entry.component}] ${entry.message}${contextStr}`;
    
    switch (entry.level) {
      case 'debug':
        console.debug(logMessage);
        break;
      case 'info':
        console.info(logMessage);
        break;
      case 'warn':
        console.warn(logMessage);
        if (entry.error) console.warn(entry.error);
        break;
      case 'error':
        console.error(logMessage);
        if (entry.error) console.error(entry.error);
        break;
    }
  }

  /**
   * Log estruturado para produção
   */
  private logStructured(entry: LogEntry): void {
    const logData = {
      timestamp: entry.timestamp.toISOString(),
      level: entry.level,
      component: entry.component,
      message: entry.message,
      ...entry.context,
      ...(entry.error && {
        error: {
          name: entry.error.name,
          message: entry.error.message,
          stack: entry.error.stack
        }
      })
    };

    console.log(JSON.stringify(logData));
  }

  /**
   * Emoji para cada nível de log
   */
  private getLevelEmoji(level: LogEntry['level']): string {
    switch (level) {
      case 'debug': return '🔍';
      case 'info': return 'ℹ️';
      case 'warn': return '⚠️';
      case 'error': return '❌';
      default: return '📝';
    }
  }

  /**
   * Cria contexto padrão para notificações
   */
  static createNotificationContext(data: {
    tenantId?: string;
    userId?: string;
    notificationType?: string;
    channel?: string;
    messageId?: string;
    templateId?: string;
  }): LogContext {
    return {
      tenantId: data.tenantId,
      userId: data.userId,
      notificationType: data.notificationType,
      channel: data.channel,
      messageId: data.messageId,
      templateId: data.templateId
    };
  }

  /**
   * Log de início de operação
   */
  startOperation(operation: string, context?: LogContext): void {
    this.info(`Iniciando ${operation}`, context);
  }

  /**
   * Log de fim de operação com sucesso
   */
  endOperation(operation: string, context?: LogContext, duration?: number): void {
    const durationStr = duration ? ` (${duration}ms)` : '';
    this.info(`${operation} concluído com sucesso${durationStr}`, context);
  }

  /**
   * Log de falha de operação
   */
  failOperation(operation: string, error: Error, context?: LogContext): void {
    this.error(`${operation} falhou`, context, error);
  }

  /**
   * Log de performance
   */
  performance(operation: string, duration: number, context?: LogContext): void {
    const level = duration > 5000 ? 'warn' : 'info';
    this.log(level, `Performance: ${operation} levou ${duration}ms`, context);
  }
}

/**
 * Classe para tratamento padronizado de erros
 */
export class NotificationError extends Error {
  public readonly code: string;
  public readonly context?: LogContext;
  public readonly originalError?: Error;

  constructor(
    message: string,
    code: string,
    context?: LogContext,
    originalError?: Error
  ) {
    super(message);
    this.name = 'NotificationError';
    this.code = code;
    this.context = context;
    this.originalError = originalError;
  }

  /**
   * Converte para objeto serializável
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      context: this.context,
      stack: this.stack,
      originalError: this.originalError ? {
        name: this.originalError.name,
        message: this.originalError.message,
        stack: this.originalError.stack
      } : undefined
    };
  }
}

/**
 * Códigos de erro padronizados
 */
export const NotificationErrorCodes = {
  // Configuração
  INVALID_CONFIG: 'INVALID_CONFIG',
  MISSING_CONFIG: 'MISSING_CONFIG',
  
  // Permissões
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  CHANNEL_DISABLED: 'CHANNEL_DISABLED',
  
  // Usuário
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_EMAIL_NOT_FOUND: 'USER_EMAIL_NOT_FOUND',
  
  // Template
  TEMPLATE_NOT_FOUND: 'TEMPLATE_NOT_FOUND',
  TEMPLATE_RENDER_ERROR: 'TEMPLATE_RENDER_ERROR',
  
  // Provedor
  PROVIDER_ERROR: 'PROVIDER_ERROR',
  PROVIDER_RATE_LIMIT: 'PROVIDER_RATE_LIMIT',
  
  // Rede
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  
  // Dados
  INVALID_DATA: 'INVALID_DATA',
  MISSING_DATA: 'MISSING_DATA',
  
  // Sistema
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR'
} as const;

export type NotificationErrorCode = typeof NotificationErrorCodes[keyof typeof NotificationErrorCodes];
