"use server";

import { createTenantServerClient } from "@/services/supabase/server";

export type FinancialStatus = "up_to_date" | "pending" | "overdue" | "no_data";

export interface StudentFinancialInfo {
    student_id: string;
    financial_status: FinancialStatus;
    next_payment_due?: string | null;
    last_payment_date?: string | null;
    overdue_amount?: number;
    pending_amount?: number;
    days_overdue?: number;
}

/**
 * Calcula o status financeiro real de um estudante baseado em:
 * - Matrículas ativas
 * - Pagamentos pendentes e vencidos
 * - Ciclo de cobrança do plano
 */
export async function calculateStudentFinancialStatus(studentId: string): Promise<StudentFinancialInfo> {
    const supabase = await createTenantServerClient();

    try {
        // Buscar matrícula ativa do estudante
        const { data: membership, error: membershipError } = await supabase
            .from('memberships')
            .select(`
        id,
        status,
        start_date,
        end_date,
        plans!memberships_plan_id_fkey(
          id,
          title,
          pricing_config
        )
      `)
            .eq('student_id', studentId)
            .eq('status', 'active')
            .single();

        if (membershipError || !membership) {
            // Estudante sem matrícula ativa = sem dados financeiros
            return {
                student_id: studentId,
                financial_status: "no_data"
            };
        }

        // Buscar pagamentos relacionados à matrícula
        const { data: payments, error: paymentsError } = await supabase
            .from('payments')
            .select('*')
            .eq('student_id', studentId)
            .eq('membership_id', membership.id)
            .order('due_date', { ascending: false });

        if (paymentsError) {
            console.error('Erro ao buscar pagamentos:', paymentsError);
            return {
                student_id: studentId,
                financial_status: "no_data"
            };
        }

        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];

        // Separar pagamentos por status
        const paidPayments = payments?.filter(p => p.status === 'paid') || [];
        const pendingPayments = payments?.filter(p => p.status === 'pending') || [];
        // Só considerar vencido se o status for 'overdue' ou se tiver overdue_date preenchido
        const overduePayments = payments?.filter(p => p.status === 'overdue' || p.overdue_date) || [];
        const upcomingPayments = pendingPayments.filter(p => !p.overdue_date);

        // Calcular valores
        const overdueAmount = overduePayments.reduce((sum, p) => sum + parseFloat(String(p.amount || '0')), 0);
        const pendingAmount = upcomingPayments.reduce((sum, p) => sum + parseFloat(String(p.amount || '0')), 0);

        // Se não há pagamentos, considerar como sem dados financeiros
        if (overdueAmount === 0 && pendingAmount === 0) {
            return {
                student_id: studentId,
                financial_status: "no_data"
            };
        }

        // Calcular dias em atraso (do pagamento mais antigo vencido)
        let daysOverdue = 0;
        if (overduePayments.length > 0) {
            const oldestOverdue = overduePayments.sort((a, b) =>
                new Date(a.due_date!).getTime() - new Date(b.due_date!).getTime()
            )[0];

            const overdueDate = new Date(oldestOverdue.due_date!);
            daysOverdue = Math.floor((today.getTime() - overdueDate.getTime()) / (1000 * 60 * 60 * 24));
        }

        // Determinar status financeiro
        let financial_status: FinancialStatus;

        if (overdueAmount > 0) {
            financial_status = "overdue";
        } else {
            // Verificar se há pagamentos pendentes que já deveriam ter sido pagos
            // (dentro dos próximos 7 dias ou vencidos hoje)
            const urgentPayments = upcomingPayments.filter(p => {
                if (!p.due_date) return false;
                const dueDate = new Date(p.due_date);
                const daysDiff = Math.floor((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                return daysDiff <= 7; // Pagamentos que vencem em até 7 dias
            });

            const urgentAmount = urgentPayments.reduce((sum, p) => sum + parseFloat(String(p.amount || '0')), 0);

            if (urgentAmount > 0) {
                financial_status = "pending";
            } else {
                financial_status = "up_to_date";
            }
        }

        // Buscar último pagamento realizado
        const lastPaidPayment = paidPayments.length > 0 ? paidPayments[0] : null;

        // Buscar próximo vencimento
        const nextPayment = upcomingPayments.length > 0 ?
            upcomingPayments.sort((a, b) =>
                new Date(a.due_date!).getTime() - new Date(b.due_date!).getTime()
            )[0] : null;

        return {
            student_id: studentId,
            financial_status,
            next_payment_due: nextPayment?.due_date || membership.next_billing_date,
            last_payment_date: lastPaidPayment?.paid_at,
            overdue_amount: overdueAmount > 0 ? overdueAmount : undefined,
            pending_amount: pendingAmount > 0 ? pendingAmount : undefined,
            days_overdue: daysOverdue > 0 ? daysOverdue : undefined
        };

    } catch (error) {
        console.error('Erro ao calcular status financeiro:', error);
        return {
            student_id: studentId,
            financial_status: "no_data"
        };
    }
}

/**
 * Calcula o status financeiro para múltiplos estudantes
 */
export async function calculateMultipleStudentsFinancialStatus(studentIds: string[]): Promise<Record<string, StudentFinancialInfo>> {
    const results: Record<string, StudentFinancialInfo> = {};

    // Processar em lotes para evitar sobrecarga
    const batchSize = 10;
    for (let i = 0; i < studentIds.length; i += batchSize) {
        const batch = studentIds.slice(i, i + batchSize);
        const batchPromises = batch.map(id => calculateStudentFinancialStatus(id));
        const batchResults = await Promise.all(batchPromises);

        batchResults.forEach(result => {
            results[result.student_id] = result;
        });
    }

    return results;
}

