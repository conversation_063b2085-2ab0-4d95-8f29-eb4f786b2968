'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Clock } from 'lucide-react';

interface QuietHoursSectionProps {
  startTime: string;
  endTime: string;
  onStartTimeChange: (time: string) => void;
  onEndTimeChange: (time: string) => void;
}

export function QuietHoursSection({
  startTime,
  endTime,
  onStartTimeChange,
  onEndTimeChange,
}: QuietHoursSectionProps) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Hor<PERSON><PERSON>s de Silêncio
        </h3>
        <p className="text-sm text-muted-foreground">
          Configure os horários em que as notificações não devem ser enviadas.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="quietHoursStart">Início do Silêncio</Label>
          <Input
            id="quietHoursStart"
            type="time"
            value={startTime}
            onChange={(e) => onStartTimeChange(e.target.value)}
          />
          <p className="text-xs text-muted-foreground">
            Horário a partir do qual as notificações serão pausadas
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="quietHoursEnd">Fim do Silêncio</Label>
          <Input
            id="quietHoursEnd"
            type="time"
            value={endTime}
            onChange={(e) => onEndTimeChange(e.target.value)}
          />
          <p className="text-xs text-muted-foreground">
            Horário a partir do qual as notificações voltam a ser enviadas
          </p>
        </div>
      </div>

      <div className="bg-muted/50 p-4 rounded-lg">
        <div className="flex items-start gap-3">
          <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full">
            <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium">Como funciona?</p>
            <p className="text-xs text-muted-foreground">
              Durante o período de silêncio ({startTime} às {endTime}), as notificações serão 
              armazenadas e enviadas apenas após o fim do período. Notificações urgentes 
              (como emergências) podem ignorar esta configuração.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
