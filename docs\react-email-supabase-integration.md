# Integração React Email + Supabase para Reset de Senha

Este guia mostra como usar templates do React Email para personalizar emails de redefinição de senha no Supabase, integrado com o sistema multi-tenant da aplicação.

## 📋 Visão Geral

A solução implementa:
- ✅ Templates React Email responsivos e personalizáveis
- ✅ Integração com sistema multi-tenant (academias)
- ✅ Edge Function para interceptar emails de autenticação
- ✅ Personalização por academia (cores, logo, contatos)
- ✅ Compatibilidade com provedores de email (Resend, SendGrid, etc.)

## 🏗️ Arquitetura

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Usuário       │    │   Supabase       │    │  Edge Function  │
│   solicita      │───▶│   Auth Hook      │───▶│  React Email    │
│   reset senha   │    │                  │    │  Template       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
                                                ┌─────────────────┐
                                                │  Provedor Email │
                                                │  (Resend/etc)   │
                                                └─────────────────┘
```

## 🚀 Configuração

### 1. Deploy da Edge Function

```bash
# Deploy da função
supabase functions deploy auth-email-hook

# Configurar variáveis de ambiente
supabase secrets set RESEND_API_KEY=your_resend_api_key
```

### 2. Configurar Auth Hook no Supabase

No dashboard do Supabase:

1. Vá para **Authentication > Hooks**
2. Crie um novo hook:
   - **Hook Type**: Send Email Hook
   - **URL**: `https://your-project.supabase.co/functions/v1/auth-email-hook`
   - **HTTP Method**: POST
   - **Enabled**: ✅

### 3. Configurar Templates no config.toml (Opcional)

Se quiser usar templates HTML simples como fallback:

```toml
[auth.email.template.recovery]
subject = "Redefinir senha - {{ .Data.AcademyName }}"
content_path = "./supabase/templates/password-reset.html"
```

### 4. Variáveis de Ambiente Necessárias

```env
# Edge Function
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
RESEND_API_KEY=your_resend_api_key

# Ou use outro provedor
SENDGRID_API_KEY=your_sendgrid_api_key
POSTMARK_API_KEY=your_postmark_api_key
```

## 📧 Usando o Template

### No código da aplicação

```typescript
import { PasswordResetTemplate } from '@/services/notifications/channels/email/templates'
import { render } from '@react-email/render'

// Renderizar template para preview ou teste
const emailHtml = render(
  <PasswordResetTemplate
    academyName="Minha Academia"
    academyLogo="https://example.com/logo.png"
    primaryColor="#007291"
    userEmail="<EMAIL>"
    userName="João Silva"
    resetUrl="https://app.com/reset?token=abc123"
    tokenHash="abc123"
    siteUrl="https://app.com"
    supportEmail="<EMAIL>"
    supportPhone="(11) 99999-9999"
  />
)
```

### Personalização por Academia

O template automaticamente busca as configurações da academia:

```sql
-- Estrutura da tabela tenants
CREATE TABLE tenants (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  logo_url TEXT,
  primary_color TEXT DEFAULT '#007291',
  secondary_color TEXT DEFAULT '#004E89',
  support_email TEXT,
  support_phone TEXT
);
```

## 🎨 Customização

### Cores e Branding

```typescript
<PasswordResetTemplate
  academyName="Fight Club"
  academyLogo="https://fightclub.com/logo.png"
  primaryColor="#ff6b35"      // Cor principal
  secondaryColor="#004e89"    // Cor secundária
  // ... outros props
/>
```

### Conteúdo Personalizado

Edite o template em `src/services/notifications/channels/email/templates/password-reset-template.tsx`:

```typescript
// Adicionar mais seções
<EmailText>
  Sua mensagem personalizada aqui
</EmailText>

// Modificar estilos
<EmailButton 
  href={resetUrl} 
  primaryColor={primaryColor}
  style={{ borderRadius: '12px' }} // Customizar estilo
>
  Redefinir Senha
</EmailButton>
```

## 🔧 Provedores de Email Suportados

### Resend (Recomendado)

```typescript
const emailResponse = await fetch('https://api.resend.com/emails', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${resendApiKey}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    from: '<EMAIL>',
    to: userEmail,
    subject: `Redefinir senha - ${academyName}`,
    html: emailHtml
  })
})
```

### SendGrid

```typescript
const emailResponse = await fetch('https://api.sendgrid.com/v3/mail/send', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${sendgridApiKey}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    personalizations: [{
      to: [{ email: userEmail }]
    }],
    from: { email: '<EMAIL>' },
    subject: `Redefinir senha - ${academyName}`,
    content: [{
      type: 'text/html',
      value: emailHtml
    }]
  })
})
```

## 🧪 Testando

### 1. Teste Local

```bash
# Servir Edge Function localmente
supabase functions serve auth-email-hook

# Testar com curl
curl -X POST http://localhost:54321/functions/v1/auth-email-hook \
  -H "Content-Type: application/json" \
  -d '{
    "event": "user.recovery",
    "user_id": "user-uuid",
    "email": "<EMAIL>",
    "email_action_type": "recovery",
    "token_hash": "test-token",
    "token": "123456",
    "site_url": "http://localhost:3000"
  }'
```

### 2. Preview do Template

Crie um arquivo de preview:

```typescript
// scripts/preview-email.ts
import { PasswordResetTemplate } from '@/services/notifications/channels/email/templates'
import { render } from '@react-email/render'
import fs from 'fs'

const html = render(
  <PasswordResetTemplate
    academyName="Academia Teste"
    userEmail="<EMAIL>"
    resetUrl="https://app.com/reset?token=preview"
    tokenHash="preview-token"
    siteUrl="https://app.com"
  />
)

fs.writeFileSync('preview.html', html)
console.log('Preview salvo em preview.html')
```

## 🚨 Troubleshooting

### Edge Function não está sendo chamada

1. Verifique se o hook está ativo no dashboard
2. Confirme a URL da Edge Function
3. Verifique os logs: `supabase functions logs auth-email-hook`

### Template não está renderizando

1. Verifique as importações do React Email
2. Confirme se o Deno está configurado corretamente
3. Teste o template isoladamente

### Emails não estão sendo enviados

1. Verifique as credenciais do provedor de email
2. Confirme se as variáveis de ambiente estão definidas
3. Verifique os logs da Edge Function

## 📚 Recursos Adicionais

- [Documentação React Email](https://react.email)
- [Supabase Auth Hooks](https://supabase.com/docs/guides/auth/auth-hooks)
- [Edge Functions](https://supabase.com/docs/guides/functions)
- [Templates do projeto](./src/services/notifications/channels/email/templates/)
